---
import Layout from '../layouts/Layout.astro';
import GridBackground from '../components/GridBackground.astro';
import Hero from '../components/Hero.tsx';
import ToolGrid from '../components/ToolGrid.tsx';
import Stats from '../components/Stats.tsx';
import '../styles/global.css';

const title = "Free Online File Converters - FormatFuse";
const description = "Convert PDF, images, documents, and more with FormatFuse. Free, fast, and private browser-based file conversion tools. No uploads required - your files never leave your device.";
const keywords = "file converter, online converter, pdf converter, image converter, document converter, free converter, privacy-first, browser-based, no upload, batch conversion, formatfuse";
---

<Layout 
  title={title}
  description={description}
  keywords={keywords}
>
  <main class="relative">
    <GridBackground />    
    <Hero client:load />
    <ToolGrid client:load />
    <Stats client:load />
  </main>
</Layout>