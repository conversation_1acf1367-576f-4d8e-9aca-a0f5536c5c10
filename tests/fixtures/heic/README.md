# HEIC Test Fixtures

Place HEIC/HEIF test files in this directory for testing the HEIC converter.

## Sample Files Needed:
- `sample.heic` - A standard HEIC image
- `sample-small.heic` - A small HEIC image for quick tests
- `sample-large.heic` - A large HEIC image for performance tests
- `corrupted.heic` - A corrupted HEIC file for error handling tests

You can obtain sample HEIC files from:
- iPhone photos (HEIC is the default format on iOS 11+)
- Online sample file repositories
- Convert existing images to HEIC using tools like ImageMagick

Note: HEIC files are not included in the repository due to licensing considerations.