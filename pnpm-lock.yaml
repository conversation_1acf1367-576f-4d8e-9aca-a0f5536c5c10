lockfileVersion: "9.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      "@astrojs/mdx":
        specifier: ^4.3.0
        version: 4.3.0(astro@5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0))
      "@astrojs/partytown":
        specifier: ^2.1.4
        version: 2.1.4
      "@astrojs/react":
        specifier: ^4.3.0
        version: 4.3.0(@types/node@24.0.3)(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(jiti@2.4.2)(lightningcss@1.30.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tsx@4.20.3)(yaml@2.8.0)
      "@astrojs/sitemap":
        specifier: ^3.4.1
        version: 3.4.1
      "@jsquash/avif":
        specifier: ^2.1.1
        version: 2.1.1
      "@jsquash/jpeg":
        specifier: ^1.6.0
        version: 1.6.0
      "@jsquash/png":
        specifier: ^3.1.1
        version: 3.1.1
      "@jsquash/resize":
        specifier: ^2.1.0
        version: 2.1.0
      "@jsquash/webp":
        specifier: ^1.5.0
        version: 1.5.0
      "@lucide/astro":
        specifier: ^0.525.0
        version: 0.525.0(astro@5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0))
      "@radix-ui/react-accordion":
        specifier: ^1.2.11
        version: 1.2.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-collapsible":
        specifier: ^1.1.11
        version: 1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-dialog":
        specifier: ^1.1.14
        version: 1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-hover-card":
        specifier: ^1.1.14
        version: 1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-label":
        specifier: ^2.1.7
        version: 2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-navigation-menu":
        specifier: ^1.2.13
        version: 1.2.13(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-progress":
        specifier: ^1.1.7
        version: 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-select":
        specifier: ^2.2.5
        version: 2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-separator":
        specifier: ^1.1.7
        version: 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-slider":
        specifier: ^1.3.5
        version: 1.3.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-slot":
        specifier: ^1.2.3
        version: 1.2.3(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-switch":
        specifier: ^1.2.5
        version: 1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-tabs":
        specifier: ^1.1.12
        version: 1.1.12(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-toggle":
        specifier: ^1.1.9
        version: 1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@refilelabs/image":
        specifier: ^0.2.5
        version: 0.2.5
      "@resvg/resvg-js":
        specifier: ^2.6.2
        version: 2.6.2
      "@resvg/resvg-wasm":
        specifier: ^2.6.2
        version: 2.6.2
      "@shikijs/langs":
        specifier: ^3.7.0
        version: 3.7.0
      "@shikijs/themes":
        specifier: ^3.7.0
        version: 3.7.0
      "@tailwindcss/vite":
        specifier: ^4.1.11
        version: 4.1.11(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      "@tanstack/react-table":
        specifier: ^8.21.3
        version: 8.21.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@tanstack/react-virtual":
        specifier: ^3.13.12
        version: 3.13.12(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@types/canvas-confetti":
        specifier: ^1.9.0
        version: 1.9.0
      "@types/js-yaml":
        specifier: ^4.0.9
        version: 4.0.9
      astro:
        specifier: ^5.10.1
        version: 5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0)
      astro-opengraph-images:
        specifier: ^1.13.1
        version: 1.13.1(canvas@3.1.2)(tw-to-css@0.0.12)
      canvas-confetti:
        specifier: ^1.9.3
        version: 1.9.3
      class-variance-authority:
        specifier: ^0.7.1
        version: 0.7.1
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      cmdk:
        specifier: ^1.1.1
        version: 1.1.1(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      comlink:
        specifier: ^4.4.2
        version: 4.4.2
      embla-carousel-react:
        specifier: ^8.6.0
        version: 8.6.0(react@19.1.0)
      file-saver:
        specifier: ^2.0.5
        version: 2.0.5
      framer-motion:
        specifier: ^12.20.1
        version: 12.20.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      js-yaml:
        specifier: ^4.1.0
        version: 4.1.0
      jszip:
        specifier: ^3.10.1
        version: 3.10.1
      libheif-js:
        specifier: ^1.19.8
        version: 1.19.8
      lucide-react:
        specifier: ^0.525.0
        version: 0.525.0(react@19.1.0)
      next-themes:
        specifier: ^0.4.6
        version: 0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      pdf-lib:
        specifier: ^1.17.1
        version: 1.17.1
      pdfjs-dist:
        specifier: ^5.3.31
        version: 5.3.31
      qrcode:
        specifier: ^1.5.4
        version: 1.5.4
      react-dom:
        specifier: ^19.1.0
        version: 19.1.0(react@19.1.0)
      react-icons:
        specifier: ^5.5.0
        version: 5.5.0(react@19.1.0)
      react-window:
        specifier: ^1.8.11
        version: 1.8.11(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      shiki:
        specifier: ^3.7.0
        version: 3.7.0
      sonner:
        specifier: ^2.0.5
        version: 2.0.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      tailwind-merge:
        specifier: ^3.3.1
        version: 3.3.1
      tailwindcss:
        specifier: ^4.1.11
        version: 4.1.11
      uuid:
        specifier: ^11.1.0
        version: 11.1.0
    devDependencies:
      "@astrojs/check":
        specifier: ^0.9.4
        version: 0.9.4(typescript@5.8.3)
      "@fontsource/inter":
        specifier: ^5.2.6
        version: 5.2.6
      "@types/file-saver":
        specifier: ^2.0.7
        version: 2.0.7
      "@types/jsdom":
        specifier: ^21.1.7
        version: 21.1.7
      "@types/react":
        specifier: ^19.1.8
        version: 19.1.8
      "@types/react-dom":
        specifier: ^19.1.6
        version: 19.1.6(@types/react@19.1.8)
      "@vitest/coverage-v8":
        specifier: ^3.2.4
        version: 3.2.4(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.0.3)(happy-dom@18.0.1)(jiti@2.4.2)(jsdom@26.1.0(canvas@3.1.2))(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      canvas:
        specifier: ^3.1.2
        version: 3.1.2
      happy-dom:
        specifier: ^18.0.1
        version: 18.0.1
      jsdom:
        specifier: ^26.1.0
        version: 26.1.0(canvas@3.1.2)
      react:
        specifier: ^19.1.0
        version: 19.1.0
      tsx:
        specifier: ^4.20.3
        version: 4.20.3
      tw-animate-css:
        specifier: ^1.3.4
        version: 1.3.4
      tw-to-css:
        specifier: ^0.0.12
        version: 0.0.12
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      vitest:
        specifier: ^3.2.4
        version: 3.2.4(@types/debug@4.1.12)(@types/node@24.0.3)(happy-dom@18.0.1)(jiti@2.4.2)(jsdom@26.1.0(canvas@3.1.2))(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)

packages:
  "@alloc/quick-lru@5.2.0":
    resolution:
      {
        integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==,
      }
    engines: { node: ">=10" }

  "@ampproject/remapping@2.3.0":
    resolution:
      {
        integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
      }
    engines: { node: ">=6.0.0" }

  "@asamuzakjp/css-color@3.2.0":
    resolution:
      {
        integrity: sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==,
      }

  "@astrojs/check@0.9.4":
    resolution:
      {
        integrity: sha512-IOheHwCtpUfvogHHsvu0AbeRZEnjJg3MopdLddkJE70mULItS/Vh37BHcI00mcOJcH1vhD3odbpvWokpxam7xA==,
      }
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0

  "@astrojs/compiler@2.12.2":
    resolution:
      {
        integrity: sha512-w2zfvhjNCkNMmMMOn5b0J8+OmUaBL1o40ipMvqcG6NRpdC+lKxmTi48DT8Xw0SzJ3AfmeFLB45zXZXtmbsjcgw==,
      }

  "@astrojs/internal-helpers@0.6.1":
    resolution:
      {
        integrity: sha512-l5Pqf6uZu31aG+3Lv8nl/3s4DbUzdlxTWDof4pEpto6GUJNhhCbelVi9dEyurOVyqaelwmS9oSyOWOENSfgo9A==,
      }

  "@astrojs/language-server@2.15.4":
    resolution:
      {
        integrity: sha512-JivzASqTPR2bao9BWsSc/woPHH7OGSGc9aMxXL4U6egVTqBycB3ZHdBJPuOCVtcGLrzdWTosAqVPz1BVoxE0+A==,
      }
    hasBin: true
    peerDependencies:
      prettier: ^3.0.0
      prettier-plugin-astro: ">=0.11.0"
    peerDependenciesMeta:
      prettier:
        optional: true
      prettier-plugin-astro:
        optional: true

  "@astrojs/markdown-remark@6.3.2":
    resolution:
      {
        integrity: sha512-bO35JbWpVvyKRl7cmSJD822e8YA8ThR/YbUsciWNA7yTcqpIAL2hJDToWP5KcZBWxGT6IOdOkHSXARSNZc4l/Q==,
      }

  "@astrojs/mdx@4.3.0":
    resolution:
      {
        integrity: sha512-OGX2KvPeBzjSSKhkCqrUoDMyzFcjKt5nTE5SFw3RdoLf0nrhyCXBQcCyclzWy1+P+XpOamn+p+hm1EhpCRyPxw==,
      }
    engines: { node: 18.20.8 || ^20.3.0 || >=22.0.0 }
    peerDependencies:
      astro: ^5.0.0

  "@astrojs/partytown@2.1.4":
    resolution:
      {
        integrity: sha512-loUrAu0cGYFDC6dHVRiomdsBJ41VjDYXPA+B3Br51V5hENFgDSOLju86OIj1TvBACcsB22UQV7BlppODDG5gig==,
      }

  "@astrojs/prism@3.3.0":
    resolution:
      {
        integrity: sha512-q8VwfU/fDZNoDOf+r7jUnMC2//H2l0TuQ6FkGJL8vD8nw/q5KiL3DS1KKBI3QhI9UQhpJ5dc7AtqfbXWuOgLCQ==,
      }
    engines: { node: 18.20.8 || ^20.3.0 || >=22.0.0 }

  "@astrojs/react@4.3.0":
    resolution:
      {
        integrity: sha512-N02aj52Iezn69qHyx5+XvPqgsPMEnel9mI5JMbGiRMTzzLMuNaxRVoQTaq2024Dpr7BLsxCjqMkNvelqMDhaHA==,
      }
    engines: { node: 18.20.8 || ^20.3.0 || >=22.0.0 }
    peerDependencies:
      "@types/react": ^17.0.50 || ^18.0.21 || ^19.0.0
      "@types/react-dom": ^17.0.17 || ^18.0.6 || ^19.0.0
      react: ^17.0.2 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.2 || ^18.0.0 || ^19.0.0

  "@astrojs/sitemap@3.4.1":
    resolution:
      {
        integrity: sha512-VjZvr1e4FH6NHyyHXOiQgLiw94LnCVY4v06wN/D0gZKchTMkg71GrAHJz81/huafcmavtLkIv26HnpfDq6/h/Q==,
      }

  "@astrojs/telemetry@3.3.0":
    resolution:
      {
        integrity: sha512-UFBgfeldP06qu6khs/yY+q1cDAaArM2/7AEIqQ9Cuvf7B1hNLq0xDrZkct+QoIGyjq56y8IaE2I3CTvG99mlhQ==,
      }
    engines: { node: 18.20.8 || ^20.3.0 || >=22.0.0 }

  "@astrojs/yaml2ts@0.2.2":
    resolution:
      {
        integrity: sha512-GOfvSr5Nqy2z5XiwqTouBBpy5FyI6DEe+/g/Mk5am9SjILN1S5fOEvYK0GuWHg98yS/dobP4m8qyqw/URW35fQ==,
      }

  "@babel/code-frame@7.27.1":
    resolution:
      {
        integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/compat-data@7.27.5":
    resolution:
      {
        integrity: sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/core@7.27.4":
    resolution:
      {
        integrity: sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/generator@7.27.5":
    resolution:
      {
        integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-compilation-targets@7.27.2":
    resolution:
      {
        integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-imports@7.27.1":
    resolution:
      {
        integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-transforms@7.27.3":
    resolution:
      {
        integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-plugin-utils@7.27.1":
    resolution:
      {
        integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-string-parser@7.27.1":
    resolution:
      {
        integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-identifier@7.27.1":
    resolution:
      {
        integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-option@7.27.1":
    resolution:
      {
        integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helpers@7.27.6":
    resolution:
      {
        integrity: sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/parser@7.27.5":
    resolution:
      {
        integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==,
      }
    engines: { node: ">=6.0.0" }
    hasBin: true

  "@babel/plugin-transform-react-jsx-self@7.27.1":
    resolution:
      {
        integrity: sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-jsx-source@7.27.1":
    resolution:
      {
        integrity: sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/runtime@7.27.6":
    resolution:
      {
        integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/template@7.27.2":
    resolution:
      {
        integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/traverse@7.27.4":
    resolution:
      {
        integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/types@7.27.6":
    resolution:
      {
        integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==,
      }
    engines: { node: ">=6.9.0" }

  "@bcoe/v8-coverage@1.0.2":
    resolution:
      {
        integrity: sha512-6zABk/ECA/QYSCQ1NGiVwwbQerUCZ+TQbp64Q3AgmfNvurHH0j8TtXa1qbShXA6qqkpAj4V5W8pP6mLe1mcMqA==,
      }
    engines: { node: ">=18" }

  "@capsizecss/unpack@2.4.0":
    resolution:
      {
        integrity: sha512-GrSU71meACqcmIUxPYOJvGKF0yryjN/L1aCuE9DViCTJI7bfkjgYDPD1zbNDcINJwSSP6UaBZY9GAbYDO7re0Q==,
      }

  "@csstools/color-helpers@5.0.2":
    resolution:
      {
        integrity: sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA==,
      }
    engines: { node: ">=18" }

  "@csstools/css-calc@2.1.4":
    resolution:
      {
        integrity: sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@csstools/css-parser-algorithms": ^3.0.5
      "@csstools/css-tokenizer": ^3.0.4

  "@csstools/css-color-parser@3.0.10":
    resolution:
      {
        integrity: sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@csstools/css-parser-algorithms": ^3.0.5
      "@csstools/css-tokenizer": ^3.0.4

  "@csstools/css-parser-algorithms@3.0.5":
    resolution:
      {
        integrity: sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      "@csstools/css-tokenizer": ^3.0.4

  "@csstools/css-tokenizer@3.0.4":
    resolution:
      {
        integrity: sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw==,
      }
    engines: { node: ">=18" }

  "@emmetio/abbreviation@2.3.3":
    resolution:
      {
        integrity: sha512-mgv58UrU3rh4YgbE/TzgLQwJ3pFsHHhCLqY20aJq+9comytTXUDNGG/SMtSeMJdkpxgXSXunBGLD8Boka3JyVA==,
      }

  "@emmetio/css-abbreviation@2.1.8":
    resolution:
      {
        integrity: sha512-s9yjhJ6saOO/uk1V74eifykk2CBYi01STTK3WlXWGOepyKa23ymJ053+DNQjpFcy1ingpaO7AxCcwLvHFY9tuw==,
      }

  "@emmetio/css-parser@0.4.0":
    resolution:
      {
        integrity: sha512-z7wkxRSZgrQHXVzObGkXG+Vmj3uRlpM11oCZ9pbaz0nFejvCDmAiNDpY75+wgXOcffKpj4rzGtwGaZxfJKsJxw==,
      }

  "@emmetio/html-matcher@1.3.0":
    resolution:
      {
        integrity: sha512-NTbsvppE5eVyBMuyGfVu2CRrLvo7J4YHb6t9sBFLyY03WYhXET37qA4zOYUjBWFCRHO7pS1B9khERtY0f5JXPQ==,
      }

  "@emmetio/scanner@1.0.4":
    resolution:
      {
        integrity: sha512-IqRuJtQff7YHHBk4G8YZ45uB9BaAGcwQeVzgj/zj8/UdOhtQpEIupUhSk8dys6spFIWVZVeK20CzGEnqR5SbqA==,
      }

  "@emmetio/stream-reader-utils@0.1.0":
    resolution:
      {
        integrity: sha512-ZsZ2I9Vzso3Ho/pjZFsmmZ++FWeEd/txqybHTm4OgaZzdS8V9V/YYWQwg5TC38Z7uLWUV1vavpLLbjJtKubR1A==,
      }

  "@emmetio/stream-reader@2.2.0":
    resolution:
      {
        integrity: sha512-fXVXEyFA5Yv3M3n8sUGT7+fvecGrZP4k6FnWWMSZVQf69kAq0LLpaBQLGcPR30m3zMmKYhECP4k/ZkzvhEW5kw==,
      }

  "@emnapi/runtime@1.4.3":
    resolution:
      {
        integrity: sha512-pBPWdu6MLKROBX05wSNKcNb++m5Er+KQ9QkB+WVM+pW2Kx9hoSrVTnu3BdkI5eBLZoKu/J6mW/B6i6bJB2ytXQ==,
      }

  "@esbuild/aix-ppc64@0.25.5":
    resolution:
      {
        integrity: sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [aix]

  "@esbuild/android-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [android]

  "@esbuild/android-arm@0.25.5":
    resolution:
      {
        integrity: sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [android]

  "@esbuild/android-x64@0.25.5":
    resolution:
      {
        integrity: sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [android]

  "@esbuild/darwin-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [darwin]

  "@esbuild/darwin-x64@0.25.5":
    resolution:
      {
        integrity: sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [darwin]

  "@esbuild/freebsd-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [freebsd]

  "@esbuild/freebsd-x64@0.25.5":
    resolution:
      {
        integrity: sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [freebsd]

  "@esbuild/linux-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [linux]

  "@esbuild/linux-arm@0.25.5":
    resolution:
      {
        integrity: sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [linux]

  "@esbuild/linux-ia32@0.25.5":
    resolution:
      {
        integrity: sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [linux]

  "@esbuild/linux-loong64@0.25.5":
    resolution:
      {
        integrity: sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==,
      }
    engines: { node: ">=18" }
    cpu: [loong64]
    os: [linux]

  "@esbuild/linux-mips64el@0.25.5":
    resolution:
      {
        integrity: sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==,
      }
    engines: { node: ">=18" }
    cpu: [mips64el]
    os: [linux]

  "@esbuild/linux-ppc64@0.25.5":
    resolution:
      {
        integrity: sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [linux]

  "@esbuild/linux-riscv64@0.25.5":
    resolution:
      {
        integrity: sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==,
      }
    engines: { node: ">=18" }
    cpu: [riscv64]
    os: [linux]

  "@esbuild/linux-s390x@0.25.5":
    resolution:
      {
        integrity: sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==,
      }
    engines: { node: ">=18" }
    cpu: [s390x]
    os: [linux]

  "@esbuild/linux-x64@0.25.5":
    resolution:
      {
        integrity: sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [linux]

  "@esbuild/netbsd-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [netbsd]

  "@esbuild/netbsd-x64@0.25.5":
    resolution:
      {
        integrity: sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [netbsd]

  "@esbuild/openbsd-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [openbsd]

  "@esbuild/openbsd-x64@0.25.5":
    resolution:
      {
        integrity: sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [openbsd]

  "@esbuild/sunos-x64@0.25.5":
    resolution:
      {
        integrity: sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [sunos]

  "@esbuild/win32-arm64@0.25.5":
    resolution:
      {
        integrity: sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [win32]

  "@esbuild/win32-ia32@0.25.5":
    resolution:
      {
        integrity: sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [win32]

  "@esbuild/win32-x64@0.25.5":
    resolution:
      {
        integrity: sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [win32]

  "@floating-ui/core@1.7.1":
    resolution:
      {
        integrity: sha512-azI0DrjMMfIug/ExbBaeDVJXcY0a7EPvPjb2xAJPa4HeimBX+Z18HK8QQR3jb6356SnDDdxx+hinMLcJEDdOjw==,
      }

  "@floating-ui/dom@1.7.1":
    resolution:
      {
        integrity: sha512-cwsmW/zyw5ltYTUeeYJ60CnQuPqmGwuGVhG9w0PRaRKkAyi38BT5CKrpIbb+jtahSwUl04cWzSx9ZOIxeS6RsQ==,
      }

  "@floating-ui/react-dom@2.1.3":
    resolution:
      {
        integrity: sha512-huMBfiU9UnQ2oBwIhgzyIiSpVgvlDstU8CX0AF+wS+KzmYMs0J2a3GwuFHV1Lz+jlrQGeC1fF+Nv0QoumyV0bA==,
      }
    peerDependencies:
      react: ">=16.8.0"
      react-dom: ">=16.8.0"

  "@floating-ui/utils@0.2.9":
    resolution:
      {
        integrity: sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg==,
      }

  "@fontsource/inter@5.2.6":
    resolution:
      {
        integrity: sha512-CZs9S1CrjD0jPwsNy9W6j0BhsmRSQrgwlTNkgQXTsAeDRM42LBRLo3eo9gCzfH4GvV7zpyf78Ozfl773826csw==,
      }

  "@img/sharp-darwin-arm64@0.33.5":
    resolution:
      {
        integrity: sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [darwin]

  "@img/sharp-darwin-x64@0.33.5":
    resolution:
      {
        integrity: sha512-fyHac4jIc1ANYGRDxtiqelIbdWkIuQaI84Mv45KvGRRxSAa7o7d1ZKAOBaYbnepLC1WqxfpimdeWfvqqSGwR2Q==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [darwin]

  "@img/sharp-libvips-darwin-arm64@1.0.4":
    resolution:
      {
        integrity: sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==,
      }
    cpu: [arm64]
    os: [darwin]

  "@img/sharp-libvips-darwin-x64@1.0.4":
    resolution:
      {
        integrity: sha512-xnGR8YuZYfJGmWPvmlunFaWJsb9T/AO2ykoP3Fz/0X5XV2aoYBPkX6xqCQvUTKKiLddarLaxpzNe+b1hjeWHAQ==,
      }
    cpu: [x64]
    os: [darwin]

  "@img/sharp-libvips-linux-arm64@1.0.4":
    resolution:
      {
        integrity: sha512-9B+taZ8DlyyqzZQnoeIvDVR/2F4EbMepXMc/NdVbkzsJbzkUjhXv/70GQJ7tdLA4YJgNP25zukcxpX2/SueNrA==,
      }
    cpu: [arm64]
    os: [linux]

  "@img/sharp-libvips-linux-arm@1.0.5":
    resolution:
      {
        integrity: sha512-gvcC4ACAOPRNATg/ov8/MnbxFDJqf/pDePbBnuBDcjsI8PssmjoKMAz4LtLaVi+OnSb5FK/yIOamqDwGmXW32g==,
      }
    cpu: [arm]
    os: [linux]

  "@img/sharp-libvips-linux-s390x@1.0.4":
    resolution:
      {
        integrity: sha512-u7Wz6ntiSSgGSGcjZ55im6uvTrOxSIS8/dgoVMoiGE9I6JAfU50yH5BoDlYA1tcuGS7g/QNtetJnxA6QEsCVTA==,
      }
    cpu: [s390x]
    os: [linux]

  "@img/sharp-libvips-linux-x64@1.0.4":
    resolution:
      {
        integrity: sha512-MmWmQ3iPFZr0Iev+BAgVMb3ZyC4KeFc3jFxnNbEPas60e1cIfevbtuyf9nDGIzOaW9PdnDciJm+wFFaTlj5xYw==,
      }
    cpu: [x64]
    os: [linux]

  "@img/sharp-libvips-linuxmusl-arm64@1.0.4":
    resolution:
      {
        integrity: sha512-9Ti+BbTYDcsbp4wfYib8Ctm1ilkugkA/uscUn6UXK1ldpC1JjiXbLfFZtRlBhjPZ5o1NCLiDbg8fhUPKStHoTA==,
      }
    cpu: [arm64]
    os: [linux]

  "@img/sharp-libvips-linuxmusl-x64@1.0.4":
    resolution:
      {
        integrity: sha512-viYN1KX9m+/hGkJtvYYp+CCLgnJXwiQB39damAO7WMdKWlIhmYTfHjwSbQeUK/20vY154mwezd9HflVFM1wVSw==,
      }
    cpu: [x64]
    os: [linux]

  "@img/sharp-linux-arm64@0.33.5":
    resolution:
      {
        integrity: sha512-JMVv+AMRyGOHtO1RFBiJy/MBsgz0x4AWrT6QoEVVTyh1E39TrCUpTRI7mx9VksGX4awWASxqCYLCV4wBZHAYxA==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  "@img/sharp-linux-arm@0.33.5":
    resolution:
      {
        integrity: sha512-JTS1eldqZbJxjvKaAkxhZmBqPRGmxgu+qFKSInv8moZ2AmT5Yib3EQ1c6gp493HvrvV8QgdOXdyaIBrhvFhBMQ==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm]
    os: [linux]

  "@img/sharp-linux-s390x@0.33.5":
    resolution:
      {
        integrity: sha512-y/5PCd+mP4CA/sPDKl2961b+C9d+vPAveS33s6Z3zfASk2j5upL6fXVPZi7ztePZ5CuH+1kW8JtvxgbuXHRa4Q==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [s390x]
    os: [linux]

  "@img/sharp-linux-x64@0.33.5":
    resolution:
      {
        integrity: sha512-opC+Ok5pRNAzuvq1AG0ar+1owsu842/Ab+4qvU879ippJBHvyY5n2mxF1izXqkPYlGuP/M556uh53jRLJmzTWA==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  "@img/sharp-linuxmusl-arm64@0.33.5":
    resolution:
      {
        integrity: sha512-XrHMZwGQGvJg2V/oRSUfSAfjfPxO+4DkiRh6p2AFjLQztWUuY/o8Mq0eMQVIY7HJ1CDQUJlxGGZRw1a5bqmd1g==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [arm64]
    os: [linux]

  "@img/sharp-linuxmusl-x64@0.33.5":
    resolution:
      {
        integrity: sha512-WT+d/cgqKkkKySYmqoZ8y3pxx7lx9vVejxW/W4DOFMYVSkErR+w7mf2u8m/y4+xHe7yY9DAXQMWQhpnMuFfScw==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [linux]

  "@img/sharp-wasm32@0.33.5":
    resolution:
      {
        integrity: sha512-ykUW4LVGaMcU9lu9thv85CbRMAwfeadCJHRsg2GmeRa/cJxsVY9Rbd57JcMxBkKHag5U/x7TSBpScF4U8ElVzg==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [wasm32]

  "@img/sharp-win32-ia32@0.33.5":
    resolution:
      {
        integrity: sha512-T36PblLaTwuVJ/zw/LaH0PdZkRz5rd3SmMHX8GSmR7vtNSP5Z6bQkExdSK7xGWyxLw4sUknBuugTelgw2faBbQ==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [ia32]
    os: [win32]

  "@img/sharp-win32-x64@0.33.5":
    resolution:
      {
        integrity: sha512-MpY/o8/8kj+EcnxwvrP4aTJSWw/aZ7JIGR4aBeZkZw5B7/Jn+tY9/VNwtcoGmdT7GfggGIU4kygOMSbYnOrAbg==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }
    cpu: [x64]
    os: [win32]

  "@isaacs/cliui@8.0.2":
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
      }
    engines: { node: ">=12" }

  "@isaacs/fs-minipass@4.0.1":
    resolution:
      {
        integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==,
      }
    engines: { node: ">=18.0.0" }

  "@istanbuljs/schema@0.1.3":
    resolution:
      {
        integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==,
      }
    engines: { node: ">=8" }

  "@jridgewell/gen-mapping@0.3.8":
    resolution:
      {
        integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/resolve-uri@3.1.2":
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/set-array@1.2.1":
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/sourcemap-codec@1.5.0":
    resolution:
      {
        integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==,
      }

  "@jridgewell/trace-mapping@0.3.25":
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
      }

  "@jsquash/avif@2.1.1":
    resolution:
      {
        integrity: sha512-LMRxd0fMgfCLtobDh0/sFYJMMiRJTNYSEEWvRDKXlAeZ08t3gI5V+1thIT0XjXJ+SVG7Zug9B0XPyx0Ti5VRNA==,
      }

  "@jsquash/jpeg@1.6.0":
    resolution:
      {
        integrity: sha512-zwN46Awh1VM6gXlIcALwb5WzqK5H2e6+Awcs1QP8AvS8ohsK/sbE4esvmH4jhlhW7+CgiUUww66vg0aTnlSIMA==,
      }

  "@jsquash/png@3.1.1":
    resolution:
      {
        integrity: sha512-C10pc+0H6j0h8fENOfnGOvkXCmvpSQTDGlfGd0sHphZhPSGTyLjIrHba0FaZZdsKqA/wlmhYicUHb92vfZphaw==,
      }

  "@jsquash/resize@2.1.0":
    resolution:
      {
        integrity: sha512-UxF45pe5a84GrleHichsnGHyFAJhOu4Ms9L1UQYXhhmDifRm4U3pMEzgly+m3xDe+h4XZyDlpr9f8FBB1LZ+LA==,
      }

  "@jsquash/webp@1.5.0":
    resolution:
      {
        integrity: sha512-KggLoj2MnRSfIqTeKe1EmbljTX2vuV7mh79k89PCL1pyqiDULcPM1L47twxXt0hkb68F70bXiL31MxsuoZtKFw==,
      }

  "@lucide/astro@0.525.0":
    resolution:
      {
        integrity: sha512-CjX9aJlmHg5/Io6mRSrSi3TbwRXtrV/6kwsRjv6eVhBymAA8ogq3j8YrV1PsXn1EHK9HFyzeJOhaXGm00acVLg==,
      }
    peerDependencies:
      astro: ^4 || ^5

  "@mdx-js/mdx@3.1.0":
    resolution:
      {
        integrity: sha512-/QxEhPAvGwbQmy1Px8F899L5Uc2KZ6JtXwlCgJmjSTBedwOZkByYcBG4GceIGPXRDsmfxhHazuS+hlOShRLeDw==,
      }

  "@napi-rs/canvas-android-arm64@0.1.71":
    resolution:
      {
        integrity: sha512-cxi3VCotIOS9kNFQI7dcysbVJi106pxryVY1Hi85pX+ZeqahRyeqc/NsLaZ998Ae99+F3HI5X/39G1Y/Byrf0A==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [android]

  "@napi-rs/canvas-darwin-arm64@0.1.71":
    resolution:
      {
        integrity: sha512-7Y4D/6vIuMLYsVNtRM/w2j0+fB1GyqeOxc7I0BTx8eLP1S6BZE2Rj6zJfdG+zmLEOW0IlHa+VQq1q2MUAjW84w==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [darwin]

  "@napi-rs/canvas-darwin-x64@0.1.71":
    resolution:
      {
        integrity: sha512-Z0IUqxclrYdfVt/SK9nKCzUHTOXKTWiygtO71YCzs0OtxKdNI7GJRJdYG48wXZEDQ/pqTF4F7Ifgtidfc2tYpg==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [darwin]

  "@napi-rs/canvas-linux-arm-gnueabihf@0.1.71":
    resolution:
      {
        integrity: sha512-KlpqqCASak5ruY+UIolJgmhMZ9Pa2o1QyaNu648L8sz4WNBbNa+aOT60XCLCL1VIKLv11B3MlNgiOHoYNmDhXQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm]
    os: [linux]

  "@napi-rs/canvas-linux-arm64-gnu@0.1.71":
    resolution:
      {
        integrity: sha512-bdGZCGu8YQNAiu3nkIVVUp6nIn6fPd36IuZsLXTG027E52KyIuZ3obCxehSwjDIUNkFWvmff5D6JYfWwAoioEw==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@napi-rs/canvas-linux-arm64-musl@0.1.71":
    resolution:
      {
        integrity: sha512-1R5sMWe9ur8uM+hAeylBwG0b6UHDR+iWQNgzXmF9vbBYRooQvmDWqpcgytKLJAC0vnWhIkKwqd7yExn7cwczmg==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@napi-rs/canvas-linux-riscv64-gnu@0.1.71":
    resolution:
      {
        integrity: sha512-xjjKsipueuG+LdKIk6/uAlqdo+rzGcmNpTZPXdakIT1sHX4NNSnQTzjRaj9Gh96Czjd9G89UWR0KIlE7fwOgFA==,
      }
    engines: { node: ">= 10" }
    cpu: [riscv64]
    os: [linux]

  "@napi-rs/canvas-linux-x64-gnu@0.1.71":
    resolution:
      {
        integrity: sha512-3s6YpklXDB4OeeULG1XTRyKrKAOo7c3HHEqM9A6N4STSjMaJtzmpp7tB/JTvAFeOeFte6gWN8IwC+7AjGJ6MpQ==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@napi-rs/canvas-linux-x64-musl@0.1.71":
    resolution:
      {
        integrity: sha512-5v9aCLzCXw7u10ray5juQMdl7TykZSn1X5AIGYwBvTAcKSgrqaR9QkRxp1Lqk3njQmFekOW1SFN9bZ/i/6y6kA==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@napi-rs/canvas-win32-x64-msvc@0.1.71":
    resolution:
      {
        integrity: sha512-oJughk6xjsRIr0Rd9EqjmZmhIMkvcPuXgr3MNn2QexTqn+YFOizrwHS5ha0BDfFl7TEGRvwaDUXBQtu8JKXb8A==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [win32]

  "@napi-rs/canvas@0.1.71":
    resolution:
      {
        integrity: sha512-92ybDocKl6JM48ZpYbj+A7Qt45IaTABDk0y3sDecEQfgdhfNzJtEityqNHoCZ4Vty2dldPkJhxgvOnbrQMXTTA==,
      }
    engines: { node: ">= 10" }

  "@nodelib/fs.scandir@2.1.5":
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.stat@2.0.5":
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.walk@1.2.8":
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
      }
    engines: { node: ">= 8" }

  "@oslojs/encoding@1.1.0":
    resolution:
      {
        integrity: sha512-70wQhgYmndg4GCPxPPxPGevRKqTIJ2Nh4OkiMWmDAVYsTQ+Ta7Sq+rPevXyXGdzr30/qZBnyOalCszoMxlyldQ==,
      }

  "@pdf-lib/standard-fonts@1.0.0":
    resolution:
      {
        integrity: sha512-hU30BK9IUN/su0Mn9VdlVKsWBS6GyhVfqjwl1FjZN4TxP6cCw0jP2w7V3Hf5uX7M0AZJ16vey9yE0ny7Sa59ZA==,
      }

  "@pdf-lib/upng@1.0.1":
    resolution:
      {
        integrity: sha512-dQK2FUMQtowVP00mtIksrlZhdFXQZPC+taih1q4CvPZ5vqdxR/LKBaFg0oAfzd1GlHZXXSPdQfzQnt+ViGvEIQ==,
      }

  "@pkgjs/parseargs@0.11.0":
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
      }
    engines: { node: ">=14" }

  "@qwik.dev/partytown@0.11.1":
    resolution:
      {
        integrity: sha512-sOjcsBsl6T+SVdLOfEYlOBssPDL5aVScA2TjUalsxvf55fMHhUaLl16JgrNi8iouhCA1hAezp74i6y8Sd9wOWw==,
      }
    engines: { node: ">=18.0.0" }
    hasBin: true

  "@radix-ui/number@1.1.1":
    resolution:
      {
        integrity: sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g==,
      }

  "@radix-ui/primitive@1.1.2":
    resolution:
      {
        integrity: sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==,
      }

  "@radix-ui/react-accordion@1.2.11":
    resolution:
      {
        integrity: sha512-l3W5D54emV2ues7jjeG1xcyN7S3jnK3zE2zHqgn0CmMsy9lNJwmgcrmaxS+7ipw15FAivzKNzH3d5EcGoFKw0A==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-arrow@1.1.7":
    resolution:
      {
        integrity: sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-collapsible@1.1.11":
    resolution:
      {
        integrity: sha512-2qrRsVGSCYasSz1RFOorXwl0H7g7J1frQtgpQgYrt+MOidtPAINHn9CPovQXb83r8ahapdx3Tu0fa/pdFFSdPg==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-collection@1.1.7":
    resolution:
      {
        integrity: sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-compose-refs@1.1.2":
    resolution:
      {
        integrity: sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-context@1.1.2":
    resolution:
      {
        integrity: sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-dialog@1.1.14":
    resolution:
      {
        integrity: sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-direction@1.1.1":
    resolution:
      {
        integrity: sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-dismissable-layer@1.1.10":
    resolution:
      {
        integrity: sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-focus-guards@1.1.2":
    resolution:
      {
        integrity: sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-focus-scope@1.1.7":
    resolution:
      {
        integrity: sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-hover-card@1.1.14":
    resolution:
      {
        integrity: sha512-CPYZ24Mhirm+g6D8jArmLzjYu4Eyg3TTUHswR26QgzXBHBe64BO/RHOJKzmF/Dxb4y4f9PKyJdwm/O/AhNkb+Q==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-id@1.1.1":
    resolution:
      {
        integrity: sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-label@2.1.7":
    resolution:
      {
        integrity: sha512-YT1GqPSL8kJn20djelMX7/cTRp/Y9w5IZHvfxQTVHrOqa2yMl7i/UfMqKRU5V7mEyKTrUVgJXhNQPVCG8PBLoQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-navigation-menu@1.2.13":
    resolution:
      {
        integrity: sha512-WG8wWfDiJlSF5hELjwfjSGOXcBR/ZMhBFCGYe8vERpC39CQYZeq1PQ2kaYHdye3V95d06H89KGMsVCIE4LWo3g==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-popper@1.2.7":
    resolution:
      {
        integrity: sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-portal@1.1.9":
    resolution:
      {
        integrity: sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-presence@1.1.4":
    resolution:
      {
        integrity: sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-primitive@2.1.3":
    resolution:
      {
        integrity: sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-progress@1.1.7":
    resolution:
      {
        integrity: sha512-vPdg/tF6YC/ynuBIJlk1mm7Le0VgW6ub6J2UWnTQ7/D23KXcPI1qy+0vBkgKgd38RCMJavBXpB83HPNFMTb0Fg==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-roving-focus@1.1.10":
    resolution:
      {
        integrity: sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-select@2.2.5":
    resolution:
      {
        integrity: sha512-HnMTdXEVuuyzx63ME0ut4+sEMYW6oouHWNGUZc7ddvUWIcfCva/AMoqEW/3wnEllriMWBa0RHspCYnfCWJQYmA==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-separator@1.1.7":
    resolution:
      {
        integrity: sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-slider@1.3.5":
    resolution:
      {
        integrity: sha512-rkfe2pU2NBAYfGaxa3Mqosi7VZEWX5CxKaanRv0vZd4Zhl9fvQrg0VM93dv3xGLGfrHuoTRF3JXH8nb9g+B3fw==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-slot@1.2.3":
    resolution:
      {
        integrity: sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-switch@1.2.5":
    resolution:
      {
        integrity: sha512-5ijLkak6ZMylXsaImpZ8u4Rlf5grRmoc0p0QeX9VJtlrM4f5m3nCTX8tWga/zOA8PZYIR/t0p2Mnvd7InrJ6yQ==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-tabs@1.1.12":
    resolution:
      {
        integrity: sha512-GTVAlRVrQrSw3cEARM0nAx73ixrWDPNZAruETn3oHCNP6SbZ/hNxdxp+u7VkIEv3/sFoLq1PfcHrl7Pnp0CDpw==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-toggle@1.1.9":
    resolution:
      {
        integrity: sha512-ZoFkBBz9zv9GWer7wIjvdRxmh2wyc2oKWw6C6CseWd6/yq1DK/l5lJ+wnsmFwJZbBYqr02mrf8A2q/CVCuM3ZA==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/react-use-callback-ref@1.1.1":
    resolution:
      {
        integrity: sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-controllable-state@1.2.2":
    resolution:
      {
        integrity: sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-effect-event@0.0.2":
    resolution:
      {
        integrity: sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-escape-keydown@1.1.1":
    resolution:
      {
        integrity: sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-layout-effect@1.1.1":
    resolution:
      {
        integrity: sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-previous@1.1.1":
    resolution:
      {
        integrity: sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-rect@1.1.1":
    resolution:
      {
        integrity: sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-use-size@1.1.1":
    resolution:
      {
        integrity: sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@radix-ui/react-visually-hidden@1.2.3":
    resolution:
      {
        integrity: sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug==,
      }
    peerDependencies:
      "@types/react": "*"
      "@types/react-dom": "*"
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true
      "@types/react-dom":
        optional: true

  "@radix-ui/rect@1.1.1":
    resolution:
      {
        integrity: sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw==,
      }

  "@refilelabs/image@0.2.5":
    resolution:
      {
        integrity: sha512-XOeVgfLzBtW2AZJtOeU/gERofMnJugK6b1LSZzhNPDMOEi7lb+gEjNUFt3Q23qAWXzulJeFoqxbC6H5bYQcG6A==,
      }

  "@resvg/resvg-js-android-arm-eabi@2.6.2":
    resolution:
      {
        integrity: sha512-FrJibrAk6v29eabIPgcTUMPXiEz8ssrAk7TXxsiZzww9UTQ1Z5KAbFJs+Z0Ez+VZTYgnE5IQJqBcoSiMebtPHA==,
      }
    engines: { node: ">= 10" }
    cpu: [arm]
    os: [android]

  "@resvg/resvg-js-android-arm64@2.6.2":
    resolution:
      {
        integrity: sha512-VcOKezEhm2VqzXpcIJoITuvUS/fcjIw5NA/w3tjzWyzmvoCdd+QXIqy3FBGulWdClvp4g+IfUemigrkLThSjAQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [android]

  "@resvg/resvg-js-darwin-arm64@2.6.2":
    resolution:
      {
        integrity: sha512-nmok2LnAd6nLUKI16aEB9ydMC6Lidiiq2m1nEBDR1LaaP7FGs4AJ90qDraxX+CWlVuRlvNjyYJTNv8qFjtL9+A==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [darwin]

  "@resvg/resvg-js-darwin-x64@2.6.2":
    resolution:
      {
        integrity: sha512-GInyZLjgWDfsVT6+SHxQVRwNzV0AuA1uqGsOAW+0th56J7Nh6bHHKXHBWzUrihxMetcFDmQMAX1tZ1fZDYSRsw==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [darwin]

  "@resvg/resvg-js-linux-arm-gnueabihf@2.6.2":
    resolution:
      {
        integrity: sha512-YIV3u/R9zJbpqTTNwTZM5/ocWetDKGsro0SWp70eGEM9eV2MerWyBRZnQIgzU3YBnSBQ1RcxRZvY/UxwESfZIw==,
      }
    engines: { node: ">= 10" }
    cpu: [arm]
    os: [linux]

  "@resvg/resvg-js-linux-arm64-gnu@2.6.2":
    resolution:
      {
        integrity: sha512-zc2BlJSim7YR4FZDQ8OUoJg5holYzdiYMeobb9pJuGDidGL9KZUv7SbiD4E8oZogtYY42UZEap7dqkkYuA91pg==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@resvg/resvg-js-linux-arm64-musl@2.6.2":
    resolution:
      {
        integrity: sha512-3h3dLPWNgSsD4lQBJPb4f+kvdOSJHa5PjTYVsWHxLUzH4IFTJUAnmuWpw4KqyQ3NA5QCyhw4TWgxk3jRkQxEKg==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@resvg/resvg-js-linux-x64-gnu@2.6.2":
    resolution:
      {
        integrity: sha512-IVUe+ckIerA7xMZ50duAZzwf1U7khQe2E0QpUxu5MBJNao5RqC0zwV/Zm965vw6D3gGFUl7j4m+oJjubBVoftw==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@resvg/resvg-js-linux-x64-musl@2.6.2":
    resolution:
      {
        integrity: sha512-UOf83vqTzoYQO9SZ0fPl2ZIFtNIz/Rr/y+7X8XRX1ZnBYsQ/tTb+cj9TE+KHOdmlTFBxhYzVkP2lRByCzqi4jQ==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@resvg/resvg-js-win32-arm64-msvc@2.6.2":
    resolution:
      {
        integrity: sha512-7C/RSgCa+7vqZ7qAbItfiaAWhyRSoD4l4BQAbVDqRRsRgY+S+hgS3in0Rxr7IorKUpGE69X48q6/nOAuTJQxeQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [win32]

  "@resvg/resvg-js-win32-ia32-msvc@2.6.2":
    resolution:
      {
        integrity: sha512-har4aPAlvjnLcil40AC77YDIk6loMawuJwFINEM7n0pZviwMkMvjb2W5ZirsNOZY4aDbo5tLx0wNMREp5Brk+w==,
      }
    engines: { node: ">= 10" }
    cpu: [ia32]
    os: [win32]

  "@resvg/resvg-js-win32-x64-msvc@2.6.2":
    resolution:
      {
        integrity: sha512-ZXtYhtUr5SSaBrUDq7DiyjOFJqBVL/dOBN7N/qmi/pO0IgiWW/f/ue3nbvu9joWE5aAKDoIzy/CxsY0suwGosQ==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [win32]

  "@resvg/resvg-js@2.6.2":
    resolution:
      {
        integrity: sha512-xBaJish5OeGmniDj9cW5PRa/PtmuVU3ziqrbr5xJj901ZDN4TosrVaNZpEiLZAxdfnhAe7uQ7QFWfjPe9d9K2Q==,
      }
    engines: { node: ">= 10" }

  "@resvg/resvg-wasm@2.6.2":
    resolution:
      {
        integrity: sha512-FqALmHI8D4o6lk/LRWDnhw95z5eO+eAa6ORjVg09YRR7BkcM6oPHU9uyC0gtQG5vpFLvgpeU4+zEAz2H8APHNw==,
      }
    engines: { node: ">= 10" }

  "@rolldown/pluginutils@1.0.0-beta.11":
    resolution:
      {
        integrity: sha512-L/gAA/hyCSuzTF1ftlzUSI/IKr2POHsv1Dd78GfqkR83KMNuswWD61JxGV2L7nRwBBBSDr6R1gCkdTmoN7W4ag==,
      }

  "@rollup/pluginutils@5.2.0":
    resolution:
      {
        integrity: sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@rollup/rollup-android-arm-eabi@4.43.0":
    resolution:
      {
        integrity: sha512-Krjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==,
      }
    cpu: [arm]
    os: [android]

  "@rollup/rollup-android-arm64@4.43.0":
    resolution:
      {
        integrity: sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==,
      }
    cpu: [arm64]
    os: [android]

  "@rollup/rollup-darwin-arm64@4.43.0":
    resolution:
      {
        integrity: sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==,
      }
    cpu: [arm64]
    os: [darwin]

  "@rollup/rollup-darwin-x64@4.43.0":
    resolution:
      {
        integrity: sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==,
      }
    cpu: [x64]
    os: [darwin]

  "@rollup/rollup-freebsd-arm64@4.43.0":
    resolution:
      {
        integrity: sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==,
      }
    cpu: [arm64]
    os: [freebsd]

  "@rollup/rollup-freebsd-x64@4.43.0":
    resolution:
      {
        integrity: sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==,
      }
    cpu: [x64]
    os: [freebsd]

  "@rollup/rollup-linux-arm-gnueabihf@4.43.0":
    resolution:
      {
        integrity: sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm-musleabihf@4.43.0":
    resolution:
      {
        integrity: sha512-ZJ3gZynL1LDSIvRfz0qXtTNs56n5DI2Mq+WACWZ7yGHFUEirHBRt7fyIk0NsCKhmRhn7WAcjgSkSVVxKlPNFFw==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm64-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-arm64-musl@4.43.0":
    resolution:
      {
        integrity: sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-loongarch64-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==,
      }
    cpu: [loong64]
    os: [linux]

  "@rollup/rollup-linux-powerpc64le-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==,
      }
    cpu: [ppc64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==,
      }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-musl@4.43.0":
    resolution:
      {
        integrity: sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==,
      }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-s390x-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==,
      }
    cpu: [s390x]
    os: [linux]

  "@rollup/rollup-linux-x64-gnu@4.43.0":
    resolution:
      {
        integrity: sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-linux-x64-musl@4.43.0":
    resolution:
      {
        integrity: sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-win32-arm64-msvc@4.43.0":
    resolution:
      {
        integrity: sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==,
      }
    cpu: [arm64]
    os: [win32]

  "@rollup/rollup-win32-ia32-msvc@4.43.0":
    resolution:
      {
        integrity: sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==,
      }
    cpu: [ia32]
    os: [win32]

  "@rollup/rollup-win32-x64-msvc@4.43.0":
    resolution:
      {
        integrity: sha512-SnGhLiE5rlK0ofq8kzuDkM0g7FN1s5VYY+YSMTibP7CqShxCQvqtNxTARS4xX4PFJfHjG0ZQYX9iGzI3FQh5Aw==,
      }
    cpu: [x64]
    os: [win32]

  "@shikijs/core@3.7.0":
    resolution:
      {
        integrity: sha512-yilc0S9HvTPyahHpcum8eonYrQtmGTU0lbtwxhA6jHv4Bm1cAdlPFRCJX4AHebkCm75aKTjjRAW+DezqD1b/cg==,
      }

  "@shikijs/engine-javascript@3.7.0":
    resolution:
      {
        integrity: sha512-0t17s03Cbv+ZcUvv+y33GtX75WBLQELgNdVghnsdhTgU3hVcWcMsoP6Lb0nDTl95ZJfbP1mVMO0p3byVh3uuzA==,
      }

  "@shikijs/engine-oniguruma@3.7.0":
    resolution:
      {
        integrity: sha512-5BxcD6LjVWsGu4xyaBC5bu8LdNgPCVBnAkWTtOCs/CZxcB22L8rcoWfv7Hh/3WooVjBZmFtyxhgvkQFedPGnFw==,
      }

  "@shikijs/langs@3.7.0":
    resolution:
      {
        integrity: sha512-1zYtdfXLr9xDKLTGy5kb7O0zDQsxXiIsw1iIBcNOO8Yi5/Y1qDbJ+0VsFoqTlzdmneO8Ij35g7QKF8kcLyznCQ==,
      }

  "@shikijs/themes@3.7.0":
    resolution:
      {
        integrity: sha512-VJx8497iZPy5zLiiCTSIaOChIcKQwR0FebwE9S3rcN0+J/GTWwQ1v/bqhTbpbY3zybPKeO8wdammqkpXc4NVjQ==,
      }

  "@shikijs/types@3.7.0":
    resolution:
      {
        integrity: sha512-MGaLeaRlSWpnP0XSAum3kP3a8vtcTsITqoEPYdt3lQG3YCdQH4DnEhodkYcNMcU0uW0RffhoD1O3e0vG5eSBBg==,
      }

  "@shikijs/vscode-textmate@10.0.2":
    resolution:
      {
        integrity: sha512-83yeghZ2xxin3Nj8z1NMd/NCuca+gsYXswywDy5bHvwlWL8tpTQmzGeUuHd9FC3E/SBEMvzJRwWEOz5gGes9Qg==,
      }

  "@shuding/opentype.js@1.4.0-beta.0":
    resolution:
      {
        integrity: sha512-3NgmNyH3l/Hv6EvsWJbsvpcpUba6R8IREQ83nH83cyakCw7uM1arZKNfHwv1Wz6jgqrF/j4x5ELvR6PnK9nTcA==,
      }
    engines: { node: ">= 8.0.0" }
    hasBin: true

  "@swc/helpers@0.5.17":
    resolution:
      {
        integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==,
      }

  "@tailwindcss/node@4.1.11":
    resolution:
      {
        integrity: sha512-yzhzuGRmv5QyU9qLNg4GTlYI6STedBWRE7NjxP45CsFYYq9taI0zJXZBMqIC/c8fViNLhmrbpSFS57EoxUmD6Q==,
      }

  "@tailwindcss/oxide-android-arm64@4.1.11":
    resolution:
      {
        integrity: sha512-3IfFuATVRUMZZprEIx9OGDjG3Ou3jG4xQzNTvjDoKmU9JdmoCohQJ83MYd0GPnQIu89YoJqvMM0G3uqLRFtetg==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [android]

  "@tailwindcss/oxide-darwin-arm64@4.1.11":
    resolution:
      {
        integrity: sha512-ESgStEOEsyg8J5YcMb1xl8WFOXfeBmrhAwGsFxxB2CxY9evy63+AtpbDLAyRkJnxLy2WsD1qF13E97uQyP1lfQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [darwin]

  "@tailwindcss/oxide-darwin-x64@4.1.11":
    resolution:
      {
        integrity: sha512-EgnK8kRchgmgzG6jE10UQNaH9Mwi2n+yw1jWmof9Vyg2lpKNX2ioe7CJdf9M5f8V9uaQxInenZkOxnTVL3fhAw==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [darwin]

  "@tailwindcss/oxide-freebsd-x64@4.1.11":
    resolution:
      {
        integrity: sha512-xdqKtbpHs7pQhIKmqVpxStnY1skuNh4CtbcyOHeX1YBE0hArj2romsFGb6yUmzkq/6M24nkxDqU8GYrKrz+UcA==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [freebsd]

  "@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11":
    resolution:
      {
        integrity: sha512-ryHQK2eyDYYMwB5wZL46uoxz2zzDZsFBwfjssgB7pzytAeCCa6glsiJGjhTEddq/4OsIjsLNMAiMlHNYnkEEeg==,
      }
    engines: { node: ">= 10" }
    cpu: [arm]
    os: [linux]

  "@tailwindcss/oxide-linux-arm64-gnu@4.1.11":
    resolution:
      {
        integrity: sha512-mYwqheq4BXF83j/w75ewkPJmPZIqqP1nhoghS9D57CLjsh3Nfq0m4ftTotRYtGnZd3eCztgbSPJ9QhfC91gDZQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@tailwindcss/oxide-linux-arm64-musl@4.1.11":
    resolution:
      {
        integrity: sha512-m/NVRFNGlEHJrNVk3O6I9ggVuNjXHIPoD6bqay/pubtYC9QIdAMpS+cswZQPBLvVvEF6GtSNONbDkZrjWZXYNQ==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [linux]

  "@tailwindcss/oxide-linux-x64-gnu@4.1.11":
    resolution:
      {
        integrity: sha512-YW6sblI7xukSD2TdbbaeQVDysIm/UPJtObHJHKxDEcW2exAtY47j52f8jZXkqE1krdnkhCMGqP3dbniu1Te2Fg==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@tailwindcss/oxide-linux-x64-musl@4.1.11":
    resolution:
      {
        integrity: sha512-e3C/RRhGunWYNC3aSF7exsQkdXzQ/M+aYuZHKnw4U7KQwTJotnWsGOIVih0s2qQzmEzOFIJ3+xt7iq67K/p56Q==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [linux]

  "@tailwindcss/oxide-wasm32-wasi@4.1.11":
    resolution:
      {
        integrity: sha512-Xo1+/GU0JEN/C/dvcammKHzeM6NqKovG+6921MR6oadee5XPBaKOumrJCXvopJ/Qb5TH7LX/UAywbqrP4lax0g==,
      }
    engines: { node: ">=14.0.0" }
    cpu: [wasm32]
    bundledDependencies:
      - "@napi-rs/wasm-runtime"
      - "@emnapi/core"
      - "@emnapi/runtime"
      - "@tybys/wasm-util"
      - "@emnapi/wasi-threads"
      - tslib

  "@tailwindcss/oxide-win32-arm64-msvc@4.1.11":
    resolution:
      {
        integrity: sha512-UgKYx5PwEKrac3GPNPf6HVMNhUIGuUh4wlDFR2jYYdkX6pL/rn73zTq/4pzUm8fOjAn5L8zDeHp9iXmUGOXZ+w==,
      }
    engines: { node: ">= 10" }
    cpu: [arm64]
    os: [win32]

  "@tailwindcss/oxide-win32-x64-msvc@4.1.11":
    resolution:
      {
        integrity: sha512-YfHoggn1j0LK7wR82TOucWc5LDCguHnoS879idHekmmiR7g9HUtMw9MI0NHatS28u/Xlkfi9w5RJWgz2Dl+5Qg==,
      }
    engines: { node: ">= 10" }
    cpu: [x64]
    os: [win32]

  "@tailwindcss/oxide@4.1.11":
    resolution:
      {
        integrity: sha512-Q69XzrtAhuyfHo+5/HMgr1lAiPP/G40OMFAnws7xcFEYqcypZmdW8eGXaOUIeOl1dzPJBPENXgbjsOyhg2nkrg==,
      }
    engines: { node: ">= 10" }

  "@tailwindcss/vite@4.1.11":
    resolution:
      {
        integrity: sha512-RHYhrR3hku0MJFRV+fN2gNbDNEh3dwKvY8XJvTxCSXeMOsCRSr+uKvDWQcbizrHgjML6ZmTE5OwMrl5wKcujCw==,
      }
    peerDependencies:
      vite: ^5.2.0 || ^6 || ^7

  "@tanstack/react-table@8.21.3":
    resolution:
      {
        integrity: sha512-5nNMTSETP4ykGegmVkhjcS8tTLW6Vl4axfEGQN3v0zdHYbK4UfoqfPChclTrJ4EoK9QynqAu9oUf8VEmrpZ5Ww==,
      }
    engines: { node: ">=12" }
    peerDependencies:
      react: ">=16.8"
      react-dom: ">=16.8"

  "@tanstack/react-virtual@3.13.12":
    resolution:
      {
        integrity: sha512-Gd13QdxPSukP8ZrkbgS2RwoZseTTbQPLnQEn7HY/rqtM+8Zt95f7xKC7N0EsKs7aoz0WzZ+fditZux+F8EzYxA==,
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  "@tanstack/table-core@8.21.3":
    resolution:
      {
        integrity: sha512-ldZXEhOBb8Is7xLs01fR3YEc3DERiz5silj8tnGkFZytt1abEvl/GhUmCE0PMLaMPTa3Jk4HbKmRlHmu+gCftg==,
      }
    engines: { node: ">=12" }

  "@tanstack/virtual-core@3.13.12":
    resolution:
      {
        integrity: sha512-1YBOJfRHV4sXUmWsFSf5rQor4Ss82G8dQWLRbnk3GA4jeP8hQt1hxXh0tmflpC0dz3VgEv/1+qwPyLeWkQuPFA==,
      }

  "@types/babel__core@7.20.5":
    resolution:
      {
        integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==,
      }

  "@types/babel__generator@7.27.0":
    resolution:
      {
        integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==,
      }

  "@types/babel__template@7.4.4":
    resolution:
      {
        integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==,
      }

  "@types/babel__traverse@7.20.7":
    resolution:
      {
        integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==,
      }

  "@types/canvas-confetti@1.9.0":
    resolution:
      {
        integrity: sha512-aBGj/dULrimR1XDZLtG9JwxX1b4HPRF6CX9Yfwh3NvstZEm1ZL7RBnel4keCPSqs1ANRu1u2Aoz9R+VmtjYuTg==,
      }

  "@types/chai@5.2.2":
    resolution:
      {
        integrity: sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==,
      }

  "@types/debug@4.1.12":
    resolution:
      {
        integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==,
      }

  "@types/deep-eql@4.0.2":
    resolution:
      {
        integrity: sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==,
      }

  "@types/estree-jsx@1.0.5":
    resolution:
      {
        integrity: sha512-52CcUVNFyfb1A2ALocQw/Dd1BQFNmSdkuC3BkZ6iqhdMfQz7JWOFRuJFloOzjk+6WijU56m9oKXFAXc7o3Towg==,
      }

  "@types/estree@1.0.7":
    resolution:
      {
        integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==,
      }

  "@types/estree@1.0.8":
    resolution:
      {
        integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==,
      }

  "@types/file-saver@2.0.7":
    resolution:
      {
        integrity: sha512-dNKVfHd/jk0SkR/exKGj2ggkB45MAkzvWCaqLUUgkyjITkGNzH8H+yUwr+BLJUBjZOe9w8X3wgmXhZDRg1ED6A==,
      }

  "@types/fontkit@2.0.8":
    resolution:
      {
        integrity: sha512-wN+8bYxIpJf+5oZdrdtaX04qUuWHcKxcDEgRS9Qm9ZClSHjzEn13SxUC+5eRM+4yXIeTYk8mTzLAWGF64847ew==,
      }

  "@types/hast@3.0.4":
    resolution:
      {
        integrity: sha512-WPs+bbQw5aCj+x6laNGWLH3wviHtoCv/P3+otBhbOhJgG8qtpdAMlTCxLtsTWA7LH1Oh/bFCHsBn0TPS5m30EQ==,
      }

  "@types/js-yaml@4.0.9":
    resolution:
      {
        integrity: sha512-k4MGaQl5TGo/iipqb2UDG2UwjXziSWkh0uysQelTlJpX1qGlpUZYm8PnO4DxG1qBomtJUdYJ6qR6xdIah10JLg==,
      }

  "@types/jsdom@21.1.7":
    resolution:
      {
        integrity: sha512-yOriVnggzrnQ3a9OKOCxaVuSug3w3/SbOj5i7VwXWZEyUNl3bLF9V3MfxGbZKuwqJOQyRfqXyROBB1CoZLFWzA==,
      }

  "@types/mdast@4.0.4":
    resolution:
      {
        integrity: sha512-kGaNbPh1k7AFzgpud/gMdvIm5xuECykRR+JnWKQno9TAXVa6WIVCGTPvYGekIDL4uwCZQSYbUxNBSb1aUo79oA==,
      }

  "@types/mdx@2.0.13":
    resolution:
      {
        integrity: sha512-+OWZQfAYyio6YkJb3HLxDrvnx6SWWDbC0zVPfBRzUk0/nqoDyf6dNxQi3eArPe8rJ473nobTMQ/8Zk+LxJ+Yuw==,
      }

  "@types/ms@2.1.0":
    resolution:
      {
        integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==,
      }

  "@types/nlcst@2.0.3":
    resolution:
      {
        integrity: sha512-vSYNSDe6Ix3q+6Z7ri9lyWqgGhJTmzRjZRqyq15N0Z/1/UnVsno9G/N40NBijoYx2seFDIl0+B2mgAb9mezUCA==,
      }

  "@types/node@17.0.45":
    resolution:
      {
        integrity: sha512-w+tIMs3rq2afQdsPJlODhoUEKzFP1ayaoyl1CcnwtIlsVe7K7bA1NGm4s3PraqTLlXnbIN84zuBlxBWo1u9BLw==,
      }

  "@types/node@20.19.1":
    resolution:
      {
        integrity: sha512-jJD50LtlD2dodAEO653i3YF04NWak6jN3ky+Ri3Em3mGR39/glWiboM/IePaRbgwSfqM1TpGXfAg8ohn/4dTgA==,
      }

  "@types/node@24.0.3":
    resolution:
      {
        integrity: sha512-R4I/kzCYAdRLzfiCabn9hxWfbuHS573x+r0dJMkkzThEa7pbrcDWK+9zu3e7aBOouf+rQAciqPFMnxwr0aWgKg==,
      }

  "@types/react-dom@19.1.6":
    resolution:
      {
        integrity: sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==,
      }
    peerDependencies:
      "@types/react": ^19.0.0

  "@types/react@19.1.8":
    resolution:
      {
        integrity: sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==,
      }

  "@types/sax@1.2.7":
    resolution:
      {
        integrity: sha512-rO73L89PJxeYM3s3pPPjiPgVVcymqU490g0YO5n5By0k2Erzj6tay/4lr1CHAAU4JyOWd1rpQ8bCf6cZfHU96A==,
      }

  "@types/tough-cookie@4.0.5":
    resolution:
      {
        integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==,
      }

  "@types/unist@2.0.11":
    resolution:
      {
        integrity: sha512-CmBKiL6NNo/OqgmMn95Fk9Whlp2mtvIv+KNpQKN2F4SjvrEesubTRWGYSg+BnWZOnlCaSTU1sMpsBOzgbYhnsA==,
      }

  "@types/unist@3.0.3":
    resolution:
      {
        integrity: sha512-ko/gIFJRv177XgZsZcBwnqJN5x/Gien8qNOn0D5bQU/zAzVf9Zt3BlcUiLqhV9y4ARk0GbT3tnUiPNgnTXzc/Q==,
      }

  "@types/whatwg-mimetype@3.0.2":
    resolution:
      {
        integrity: sha512-c2AKvDT8ToxLIOUlN51gTiHXflsfIFisS4pO7pDPoKouJCESkhZnEy623gwP9laCy5lnLDAw1vAzu2vM2YLOrA==,
      }

  "@ungap/structured-clone@1.3.0":
    resolution:
      {
        integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==,
      }

  "@vitejs/plugin-react@4.5.2":
    resolution:
      {
        integrity: sha512-QNVT3/Lxx99nMQWJWF7K4N6apUEuT0KlZA3mx/mVaoGj3smm/8rc8ezz15J1pcbcjDK0V15rpHetVfya08r76Q==,
      }
    engines: { node: ^14.18.0 || >=16.0.0 }
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0

  "@vitest/coverage-v8@3.2.4":
    resolution:
      {
        integrity: sha512-EyF9SXU6kS5Ku/U82E259WSnvg6c8KTjppUncuNdm5QHpe17mwREHnjDzozC8x9MZ0xfBUFSaLkRv4TMA75ALQ==,
      }
    peerDependencies:
      "@vitest/browser": 3.2.4
      vitest: 3.2.4
    peerDependenciesMeta:
      "@vitest/browser":
        optional: true

  "@vitest/expect@3.2.4":
    resolution:
      {
        integrity: sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==,
      }

  "@vitest/mocker@3.2.4":
    resolution:
      {
        integrity: sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==,
      }
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0 || ^6.0.0 || ^7.0.0-0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true

  "@vitest/pretty-format@3.2.4":
    resolution:
      {
        integrity: sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==,
      }

  "@vitest/runner@3.2.4":
    resolution:
      {
        integrity: sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==,
      }

  "@vitest/snapshot@3.2.4":
    resolution:
      {
        integrity: sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==,
      }

  "@vitest/spy@3.2.4":
    resolution:
      {
        integrity: sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==,
      }

  "@vitest/utils@3.2.4":
    resolution:
      {
        integrity: sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==,
      }

  "@volar/kit@2.4.14":
    resolution:
      {
        integrity: sha512-kBcmHjEodtmYGJELHePZd2JdeYm4ZGOd9F/pQ1YETYIzAwy4Z491EkJ1nRSo/GTxwKt0XYwYA/dHSEgXecVHRA==,
      }
    peerDependencies:
      typescript: "*"

  "@volar/language-core@2.4.14":
    resolution:
      {
        integrity: sha512-X6beusV0DvuVseaOEy7GoagS4rYHgDHnTrdOj5jeUb49fW5ceQyP9Ej5rBhqgz2wJggl+2fDbbojq1XKaxDi6w==,
      }

  "@volar/language-server@2.4.14":
    resolution:
      {
        integrity: sha512-P3mGbQbW0v40UYBnb3DAaNtRYx6/MGOVKzdOWmBCGwjUkCR2xBkGrCFt05XnPDwFS/cTWDh2U6Mc9lpZ8Aecfw==,
      }

  "@volar/language-service@2.4.14":
    resolution:
      {
        integrity: sha512-vNC3823EJohdzLTyjZoCMPwoWCfINB5emusniCkW5CGoGHQov4VVmT6yI5ncgP/NpgAIUv2NEkJooXvLHA4VeQ==,
      }

  "@volar/source-map@2.4.14":
    resolution:
      {
        integrity: sha512-5TeKKMh7Sfxo8021cJfmBzcjfY1SsXsPMMjMvjY7ivesdnybqqS+GxGAoXHAOUawQTwtdUxgP65Im+dEmvWtYQ==,
      }

  "@volar/typescript@2.4.14":
    resolution:
      {
        integrity: sha512-p8Z6f/bZM3/HyCdRNFZOEEzts51uV8WHeN8Tnfnm2EBv6FDB2TQLzfVx7aJvnl8ofKAOnS64B2O8bImBFaauRw==,
      }

  "@vscode/emmet-helper@2.11.0":
    resolution:
      {
        integrity: sha512-QLxjQR3imPZPQltfbWRnHU6JecWTF1QSWhx3GAKQpslx7y3Dp6sIIXhKjiUJ/BR9FX8PVthjr9PD6pNwOJfAzw==,
      }

  "@vscode/l10n@0.0.18":
    resolution:
      {
        integrity: sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==,
      }

  acorn-jsx@5.3.2:
    resolution:
      {
        integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==,
      }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution:
      {
        integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==,
      }
    engines: { node: ">=0.4.0" }
    hasBin: true

  agent-base@7.1.3:
    resolution:
      {
        integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==,
      }
    engines: { node: ">= 14" }

  ajv@8.17.1:
    resolution:
      {
        integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==,
      }

  ansi-align@3.0.1:
    resolution:
      {
        integrity: sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==,
      }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@6.1.0:
    resolution:
      {
        integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==,
      }
    engines: { node: ">=12" }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
      }
    engines: { node: ">=8" }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
      }
    engines: { node: ">=12" }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
      }
    engines: { node: ">= 8" }

  arg@5.0.2:
    resolution:
      {
        integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==,
      }

  argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
      }

  aria-hidden@1.2.6:
    resolution:
      {
        integrity: sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA==,
      }
    engines: { node: ">=10" }

  aria-query@5.3.2:
    resolution:
      {
        integrity: sha512-COROpnaoap1E2F000S62r6A60uHZnmlvomhfyT2DlTcrY1OrBKn2UhH7qn5wTC9zMvD0AY7csdPSNwKP+7WiQw==,
      }
    engines: { node: ">= 0.4" }

  array-iterate@2.0.1:
    resolution:
      {
        integrity: sha512-I1jXZMjAgCMmxT4qxXfPXa6SthSoE8h6gkSI9BGGNv8mP8G/v0blc+qFnZu6K42vTOiuME596QaLO0TP3Lk0xg==,
      }

  assertion-error@2.0.1:
    resolution:
      {
        integrity: sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==,
      }
    engines: { node: ">=12" }

  ast-v8-to-istanbul@0.3.3:
    resolution:
      {
        integrity: sha512-MuXMrSLVVoA6sYN/6Hke18vMzrT4TZNbZIj/hvh0fnYFpO+/kFXcLIaiPwXXWaQUPg4yJD8fj+lfJ7/1EBconw==,
      }

  astring@1.9.0:
    resolution:
      {
        integrity: sha512-LElXdjswlqjWrPpJFg1Fx4wpkOCxj1TDHlSV4PlaRxHGWko024xICaa97ZkMfs6DRKlCguiAI+rbXv5GWwXIkg==,
      }
    hasBin: true

  astro-opengraph-images@1.13.1:
    resolution:
      {
        integrity: sha512-HOuyiQzYKpixGRI48ZHIsn5YeXj6C4bmwUNEjlYEHDrYhfBteCi8ypSdZf+Sqa5il/zEuIJA3vBYJMGgPKp+XQ==,
      }
    peerDependencies:
      tw-to-css: ^0.0.12

  astro@5.10.1:
    resolution:
      {
        integrity: sha512-DJVmt+51jU1xmgmAHCDwuUgcG/5aVFSU+tcX694acAZqPVt8EMUAmUZcJDX36Z7/EztnPph9HR3pm72jS2EgHQ==,
      }
    engines:
      { node: 18.20.8 || ^20.3.0 || >=22.0.0, npm: ">=9.6.5", pnpm: ">=7.1.0" }
    hasBin: true

  axobject-query@4.1.0:
    resolution:
      {
        integrity: sha512-qIj0G9wZbMGNLjLmg1PT6v2mE9AH2zlnADJD/2tC6E00hgmhUOfEB6greHPAfLRSufHqROIUTkw6E+M3lH0PTQ==,
      }
    engines: { node: ">= 0.4" }

  bail@2.0.2:
    resolution:
      {
        integrity: sha512-0xO6mYd7JB2YesxDKplafRpsiOzPt9V02ddPCLbY1xYGPOX24NTyN50qnUxgCPcSoYMhKpAuBTjQoRZCAkUDRw==,
      }

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
      }

  base-64@1.0.0:
    resolution:
      {
        integrity: sha512-kwDPIFCGx0NZHog36dj+tHiwP4QMzsZ3AgMViUBKI0+V5n4U0ufTCUMhnQ04diaRI8EX/QcPfql7zlhZ7j4zgg==,
      }

  base64-js@0.0.8:
    resolution:
      {
        integrity: sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==,
      }
    engines: { node: ">= 0.4" }

  base64-js@1.5.1:
    resolution:
      {
        integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==,
      }

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
      }
    engines: { node: ">=8" }

  bl@4.1.0:
    resolution:
      {
        integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==,
      }

  blob-to-buffer@1.2.9:
    resolution:
      {
        integrity: sha512-BF033y5fN6OCofD3vgHmNtwZWRcq9NLyyxyILx9hfMy1sXYy4ojFl765hJ2lP0YaN2fuxPaLO2Vzzoxy0FLFFA==,
      }

  boxen@8.0.1:
    resolution:
      {
        integrity: sha512-F3PH5k5juxom4xktynS7MoFY+NUWH5LC4CnH11YB8NPew+HLpmBLCybSAEyb2F+4pRXhuhWqFesoQd6DAyc2hw==,
      }
    engines: { node: ">=18" }

  brace-expansion@2.0.2:
    resolution:
      {
        integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==,
      }

  braces@3.0.3:
    resolution:
      {
        integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
      }
    engines: { node: ">=8" }

  brotli@1.3.3:
    resolution:
      {
        integrity: sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==,
      }

  browserslist@4.25.0:
    resolution:
      {
        integrity: sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==,
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  buffer@5.7.1:
    resolution:
      {
        integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==,
      }

  cac@6.7.14:
    resolution:
      {
        integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
      }
    engines: { node: ">=8" }

  camelcase-css@2.0.1:
    resolution:
      {
        integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==,
      }
    engines: { node: ">= 6" }

  camelcase@5.3.1:
    resolution:
      {
        integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==,
      }
    engines: { node: ">=6" }

  camelcase@8.0.0:
    resolution:
      {
        integrity: sha512-8WB3Jcas3swSvjIeA2yvCJ+Miyz5l1ZmB6HFb9R1317dt9LCQoswg/BGrmAmkWVEszSrrg4RwmO46qIm2OEnSA==,
      }
    engines: { node: ">=16" }

  camelize@1.0.1:
    resolution:
      {
        integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ==,
      }

  caniuse-lite@1.0.30001723:
    resolution:
      {
        integrity: sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==,
      }

  canvas-confetti@1.9.3:
    resolution:
      {
        integrity: sha512-rFfTURMvmVEX1gyXFgn5QMn81bYk70qa0HLzcIOSVEyl57n6o9ItHeBtUSWdvKAPY0xlvBHno4/v3QPrT83q9g==,
      }

  canvas@3.1.2:
    resolution:
      {
        integrity: sha512-Z/tzFAcBzoCvJlOSlCnoekh1Gu8YMn0J51+UAuXJAbW1Z6I9l2mZgdD7738MepoeeIcUdDtbMnOg6cC7GJxy/g==,
      }
    engines: { node: ^18.12.0 || >= 20.9.0 }

  ccount@2.0.1:
    resolution:
      {
        integrity: sha512-eyrF0jiFpY+3drT6383f1qhkbGsLSifNAjA61IUjZjmLCWjItY6LB9ft9YhoDgwfmclB2zhu51Lc7+95b8NRAg==,
      }

  chai@5.2.0:
    resolution:
      {
        integrity: sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==,
      }
    engines: { node: ">=12" }

  chalk@5.4.1:
    resolution:
      {
        integrity: sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==,
      }
    engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

  character-entities-html4@2.1.0:
    resolution:
      {
        integrity: sha512-1v7fgQRj6hnSwFpq1Eu0ynr/CDEw0rXo2B61qXrLNdHZmPKgb7fqS1a2JwF0rISo9q77jDI8VMEHoApn8qDoZA==,
      }

  character-entities-legacy@3.0.0:
    resolution:
      {
        integrity: sha512-RpPp0asT/6ufRm//AJVwpViZbGM/MkjQFxJccQRHmISF/22NBtsHqAWmL+/pmkPWoIUJdWyeVleTl1wydHATVQ==,
      }

  character-entities@2.0.2:
    resolution:
      {
        integrity: sha512-shx7oQ0Awen/BRIdkjkvz54PnEEI/EjwXDSIZp86/KKdbafHh1Df/RYGBhn4hbe2+uKC9FnT5UCEdyPz3ai9hQ==,
      }

  character-reference-invalid@2.0.1:
    resolution:
      {
        integrity: sha512-iBZ4F4wRbyORVsu0jPV7gXkOsGYjGHPmAyv+HiHG8gi5PtC9KI2j1+v8/tlibRvjoWX027ypmG/n0HtO5t7unw==,
      }

  check-error@2.1.1:
    resolution:
      {
        integrity: sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==,
      }
    engines: { node: ">= 16" }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
      }
    engines: { node: ">= 8.10.0" }

  chokidar@4.0.3:
    resolution:
      {
        integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==,
      }
    engines: { node: ">= 14.16.0" }

  chownr@1.1.4:
    resolution:
      {
        integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==,
      }

  chownr@3.0.0:
    resolution:
      {
        integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==,
      }
    engines: { node: ">=18" }

  ci-info@4.2.0:
    resolution:
      {
        integrity: sha512-cYY9mypksY8NRqgDB1XD1RiJL338v/551niynFTGkZOO2LHuB2OmOYxDIe/ttN9AHwrqdum1360G3ald0W9kCg==,
      }
    engines: { node: ">=8" }

  class-variance-authority@0.7.1:
    resolution:
      {
        integrity: sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg==,
      }

  cli-boxes@3.0.0:
    resolution:
      {
        integrity: sha512-/lzGpEWL/8PfI0BmBOPRwp0c/wFNX1RdUML3jK/RcSBA9T8mZDdQpqYBKtCFTOfQbwPqWEOpjqW+Fnayc0969g==,
      }
    engines: { node: ">=10" }

  cliui@6.0.0:
    resolution:
      {
        integrity: sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==,
      }

  cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
      }
    engines: { node: ">=12" }

  clone@2.1.2:
    resolution:
      {
        integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==,
      }
    engines: { node: ">=0.8" }

  clsx@2.1.1:
    resolution:
      {
        integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==,
      }
    engines: { node: ">=6" }

  cmdk@1.1.1:
    resolution:
      {
        integrity: sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg==,
      }
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      react-dom: ^18 || ^19 || ^19.0.0-rc

  collapse-white-space@2.1.0:
    resolution:
      {
        integrity: sha512-loKTxY1zCOuG4j9f6EPnuyyYkf58RnhhWTvRoZEokgB+WbdXehfjFviyOVYkqzEWz1Q5kRiZdBYS5SwxbQYwzw==,
      }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
      }
    engines: { node: ">=7.0.0" }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
      }

  color-string@1.9.1:
    resolution:
      {
        integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==,
      }

  color@4.2.3:
    resolution:
      {
        integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==,
      }
    engines: { node: ">=12.5.0" }

  comlink@4.4.2:
    resolution:
      {
        integrity: sha512-OxGdvBmJuNKSCMO4NTl1L47VRp6xn2wG4F/2hYzB6tiCb709otOxtEYCSvK80PtjODfXXZu8ds+Nw5kVCjqd2g==,
      }

  comma-separated-tokens@2.0.3:
    resolution:
      {
        integrity: sha512-Fu4hJdvzeylCfQPp9SGWidpzrMs7tTrlu6Vb8XGaRGck8QSNZJJp538Wrb60Lax4fPwR64ViY468OIUTbRlGZg==,
      }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
      }
    engines: { node: ">= 6" }

  common-ancestor-path@1.0.1:
    resolution:
      {
        integrity: sha512-L3sHRo1pXXEqX8VU28kfgUY+YGsk09hPqZiZmLacNib6XNTCM8ubYeT7ryXQw8asB1sKgcU5lkB7ONug08aB8w==,
      }

  convert-source-map@2.0.0:
    resolution:
      {
        integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
      }

  cookie-es@1.2.2:
    resolution:
      {
        integrity: sha512-+W7VmiVINB+ywl1HGXJXmrqkOhpKrIiVZV6tQuV54ZyQC7MMuBt81Vc336GMLoHBq5hV/F9eXgt5Mnx0Rha5Fg==,
      }

  cookie@1.0.2:
    resolution:
      {
        integrity: sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==,
      }
    engines: { node: ">=18" }

  core-util-is@1.0.3:
    resolution:
      {
        integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==,
      }

  cross-fetch@3.2.0:
    resolution:
      {
        integrity: sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==,
      }

  cross-spawn@7.0.6:
    resolution:
      {
        integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==,
      }
    engines: { node: ">= 8" }

  crossws@0.3.5:
    resolution:
      {
        integrity: sha512-ojKiDvcmByhwa8YYqbQI/hg7MEU0NC03+pSdEq4ZUnZR9xXpwk7E43SMNGkn+JxJGPFtNvQ48+vV2p+P1ml5PA==,
      }

  css-background-parser@0.1.0:
    resolution:
      {
        integrity: sha512-2EZLisiZQ+7m4wwur/qiYJRniHX4K5Tc9w93MT3AS0WS1u5kaZ4FKXlOTBhOjc+CgEgPiGY+fX1yWD8UwpEqUA==,
      }

  css-box-shadow@1.0.0-3:
    resolution:
      {
        integrity: sha512-9jaqR6e7Ohds+aWwmhe6wILJ99xYQbfmK9QQB9CcMjDbTxPZjwEmUQpU91OG05Xgm8BahT5fW+svbsQGjS/zPg==,
      }

  css-color-keywords@1.0.0:
    resolution:
      {
        integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg==,
      }
    engines: { node: ">=4" }

  css-gradient-parser@0.0.16:
    resolution:
      {
        integrity: sha512-3O5QdqgFRUbXvK1x5INf1YkBz1UKSWqrd63vWsum8MNHDBYD5urm3QtxZbKU259OrEXNM26lP/MPY3d1IGkBgA==,
      }
    engines: { node: ">=16" }

  css-to-react-native@3.2.0:
    resolution:
      {
        integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ==,
      }

  css-tree@3.1.0:
    resolution:
      {
        integrity: sha512-0eW44TGN5SQXU1mWSkKwFstI/22X2bG1nYzZTYMAWjylYURhse752YgbE4Cx46AC+bAvI+/dYTPRk1LqSUnu6w==,
      }
    engines: { node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0 }

  cssesc@3.0.0:
    resolution:
      {
        integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==,
      }
    engines: { node: ">=4" }
    hasBin: true

  cssstyle@4.4.0:
    resolution:
      {
        integrity: sha512-W0Y2HOXlPkb2yaKrCVRjinYKciu/qSLEmK0K9mcfDei3zwlnHFEHAs/Du3cIRwPqY+J4JsiBzUjoHyc8RsJ03A==,
      }
    engines: { node: ">=18" }

  csstype@3.1.3:
    resolution:
      {
        integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==,
      }

  data-urls@5.0.0:
    resolution:
      {
        integrity: sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==,
      }
    engines: { node: ">=18" }

  debug@4.4.1:
    resolution:
      {
        integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==,
      }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution:
      {
        integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==,
      }
    engines: { node: ">=0.10.0" }

  decimal.js@10.5.0:
    resolution:
      {
        integrity: sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==,
      }

  decode-named-character-reference@1.2.0:
    resolution:
      {
        integrity: sha512-c6fcElNV6ShtZXmsgNgFFV5tVX2PaV4g+MOAkb8eXHvn6sryJBrZa9r0zV6+dtTyoCKxtDy5tyQ5ZwQuidtd+Q==,
      }

  decompress-response@6.0.0:
    resolution:
      {
        integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==,
      }
    engines: { node: ">=10" }

  deep-eql@5.0.2:
    resolution:
      {
        integrity: sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==,
      }
    engines: { node: ">=6" }

  deep-extend@0.6.0:
    resolution:
      {
        integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==,
      }
    engines: { node: ">=4.0.0" }

  defu@6.1.4:
    resolution:
      {
        integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==,
      }

  dequal@2.0.3:
    resolution:
      {
        integrity: sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==,
      }
    engines: { node: ">=6" }

  destr@2.0.5:
    resolution:
      {
        integrity: sha512-ugFTXCtDZunbzasqBxrK93Ik/DRYsO6S/fedkWEMKqt04xZ4csmnmwGDBAb07QWNaGMAmnTIemsYZCksjATwsA==,
      }

  detect-libc@2.0.4:
    resolution:
      {
        integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==,
      }
    engines: { node: ">=8" }

  detect-node-es@1.1.0:
    resolution:
      {
        integrity: sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==,
      }

  deterministic-object-hash@2.0.2:
    resolution:
      {
        integrity: sha512-KxektNH63SrbfUyDiwXqRb1rLwKt33AmMv+5Nhsw1kqZ13SJBRTgZHtGbE+hH3a1mVW1cz+4pqSWVPAtLVXTzQ==,
      }
    engines: { node: ">=18" }

  devalue@5.1.1:
    resolution:
      {
        integrity: sha512-maua5KUiapvEwiEAe+XnlZ3Rh0GD+qI1J/nb9vrJc3muPXvcF/8gXYTWF76+5DAqHyDUtOIImEuo0YKE9mshVw==,
      }

  devlop@1.1.0:
    resolution:
      {
        integrity: sha512-RWmIqhcFf1lRYBvNmr7qTNuyCt/7/ns2jbpp1+PalgE/rDQcBT0fioSMUpJ93irlUhC5hrg4cYqe6U+0ImW0rA==,
      }

  dfa@1.2.0:
    resolution:
      {
        integrity: sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q==,
      }

  didyoumean@1.2.2:
    resolution:
      {
        integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==,
      }

  diff@5.2.0:
    resolution:
      {
        integrity: sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==,
      }
    engines: { node: ">=0.3.1" }

  dijkstrajs@1.0.3:
    resolution:
      {
        integrity: sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==,
      }

  dlv@1.1.3:
    resolution:
      {
        integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==,
      }

  dotenv@16.5.0:
    resolution:
      {
        integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==,
      }
    engines: { node: ">=12" }

  dset@3.1.4:
    resolution:
      {
        integrity: sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==,
      }
    engines: { node: ">=4" }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
      }

  electron-to-chromium@1.5.169:
    resolution:
      {
        integrity: sha512-q7SQx6mkLy0GTJK9K9OiWeaBMV4XQtBSdf6MJUzDB/H/5tFXfIiX38Lci1Kl6SsgiEhz1SQI1ejEOU5asWEhwQ==,
      }

  embla-carousel-react@8.6.0:
    resolution:
      {
        integrity: sha512-0/PjqU7geVmo6F734pmPqpyHqiM99olvyecY7zdweCw+6tKEXnrE90pBiBbMMU8s5tICemzpQ3hi5EpxzGW+JA==,
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  embla-carousel-reactive-utils@8.6.0:
    resolution:
      {
        integrity: sha512-fMVUDUEx0/uIEDM0Mz3dHznDhfX+znCCDCeIophYb1QGVM7YThSWX+wz11zlYwWFOr74b4QLGg0hrGPJeG2s4A==,
      }
    peerDependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0:
    resolution:
      {
        integrity: sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==,
      }

  emmet@2.4.11:
    resolution:
      {
        integrity: sha512-23QPJB3moh/U9sT4rQzGgeyyGIrcM+GH5uVYg2C6wZIxAIJq7Ng3QLT79tl8FUwDXhyq9SusfknOrofAKqvgyQ==,
      }

  emoji-regex-xs@2.0.1:
    resolution:
      {
        integrity: sha512-1QFuh8l7LqUcKe24LsPUNzjrzJQ7pgRwp1QMcZ5MX6mFplk2zQ08NVCM84++1cveaUUYtcCYHmeFEuNg16sU4g==,
      }
    engines: { node: ">=10.0.0" }

  emoji-regex@10.4.0:
    resolution:
      {
        integrity: sha512-EC+0oUMY1Rqm4O6LLrgjtYDvcVYTy7chDnM4Q7030tP4Kwj3u/pR6gP9ygnp2CJMK5Gq+9Q2oqmrFJAz01DXjw==,
      }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
      }

  end-of-stream@1.4.5:
    resolution:
      {
        integrity: sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==,
      }

  enhanced-resolve@5.18.1:
    resolution:
      {
        integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==,
      }
    engines: { node: ">=10.13.0" }

  entities@6.0.1:
    resolution:
      {
        integrity: sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==,
      }
    engines: { node: ">=0.12" }

  es-module-lexer@1.7.0:
    resolution:
      {
        integrity: sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==,
      }

  esast-util-from-estree@2.0.0:
    resolution:
      {
        integrity: sha512-4CyanoAudUSBAn5K13H4JhsMH6L9ZP7XbLVe/dKybkxMO7eDyLsT8UHl9TRNrU2Gr9nz+FovfSIjuXWJ81uVwQ==,
      }

  esast-util-from-js@2.0.1:
    resolution:
      {
        integrity: sha512-8Ja+rNJ0Lt56Pcf3TAmpBZjmx8ZcK5Ts4cAzIOjsjevg9oSXJnl6SUQ2EevU8tv3h6ZLWmoKL5H4fgWvdvfETw==,
      }

  esbuild@0.25.5:
    resolution:
      {
        integrity: sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==,
      }
    engines: { node: ">=18" }
    hasBin: true

  escalade@3.2.0:
    resolution:
      {
        integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==,
      }
    engines: { node: ">=6" }

  escape-html@1.0.3:
    resolution:
      {
        integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==,
      }

  escape-string-regexp@1.0.5:
    resolution:
      {
        integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==,
      }
    engines: { node: ">=0.8.0" }

  escape-string-regexp@5.0.0:
    resolution:
      {
        integrity: sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==,
      }
    engines: { node: ">=12" }

  estree-util-attach-comments@3.0.0:
    resolution:
      {
        integrity: sha512-cKUwm/HUcTDsYh/9FgnuFqpfquUbwIqwKM26BVCGDPVgvaCl/nDCCjUfiLlx6lsEZ3Z4RFxNbOQ60pkaEwFxGw==,
      }

  estree-util-build-jsx@3.0.1:
    resolution:
      {
        integrity: sha512-8U5eiL6BTrPxp/CHbs2yMgP8ftMhR5ww1eIKoWRMlqvltHF8fZn5LRDvTKuxD3DUn+shRbLGqXemcP51oFCsGQ==,
      }

  estree-util-is-identifier-name@3.0.0:
    resolution:
      {
        integrity: sha512-hFtqIDZTIUZ9BXLb8y4pYGyk6+wekIivNVTcmvk8NoOh+VeRn5y6cEHzbURrWbfp1fIqdVipilzj+lfaadNZmg==,
      }

  estree-util-scope@1.0.0:
    resolution:
      {
        integrity: sha512-2CAASclonf+JFWBNJPndcOpA8EMJwa0Q8LUFJEKqXLW6+qBvbFZuF5gItbQOs/umBUkjviCSDCbBwU2cXbmrhQ==,
      }

  estree-util-to-js@2.0.0:
    resolution:
      {
        integrity: sha512-WDF+xj5rRWmD5tj6bIqRi6CkLIXbbNQUcxQHzGysQzvHmdYG2G7p/Tf0J0gpxGgkeMZNTIjT/AoSvC9Xehcgdg==,
      }

  estree-util-visit@2.0.0:
    resolution:
      {
        integrity: sha512-m5KgiH85xAhhW8Wta0vShLcUvOsh3LLPI2YVwcbio1l7E09NTLL1EyMZFM1OyWowoH0skScNbhOPl4kcBgzTww==,
      }

  estree-walker@2.0.2:
    resolution:
      {
        integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==,
      }

  estree-walker@3.0.3:
    resolution:
      {
        integrity: sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==,
      }

  eventemitter3@5.0.1:
    resolution:
      {
        integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==,
      }

  expand-template@2.0.3:
    resolution:
      {
        integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==,
      }
    engines: { node: ">=6" }

  expect-type@1.2.1:
    resolution:
      {
        integrity: sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==,
      }
    engines: { node: ">=12.0.0" }

  extend@3.0.2:
    resolution:
      {
        integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==,
      }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
      }

  fast-glob@3.3.3:
    resolution:
      {
        integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==,
      }
    engines: { node: ">=8.6.0" }

  fast-uri@3.0.6:
    resolution:
      {
        integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==,
      }

  fastq@1.19.1:
    resolution:
      {
        integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==,
      }

  fdir@6.4.6:
    resolution:
      {
        integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==,
      }
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  fflate@0.7.4:
    resolution:
      {
        integrity: sha512-5u2V/CDW15QM1XbbgS+0DfPxVB+jUKhWEKuuFuHncbk3tEEqzmoXL+2KyOFuKGqOnmdIy0/davWF1CkuwtibCw==,
      }

  file-saver@2.0.5:
    resolution:
      {
        integrity: sha512-P9bmyZ3h/PRG+Nzga+rbdI4OEpNDzAVyy74uVO9ATgzLK6VtAsYybF/+TOCvrc0MO793d6+42lLyZTw7/ArVzA==,
      }

  fill-range@7.1.1:
    resolution:
      {
        integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
      }
    engines: { node: ">=8" }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==,
      }
    engines: { node: ">=8" }

  flattie@1.1.1:
    resolution:
      {
        integrity: sha512-9UbaD6XdAL97+k/n+N7JwX46K/M6Zc6KcFYskrYL8wbBV/Uyk0CTAMY0VT+qiK5PM7AIc9aTWYtq65U7T+aCNQ==,
      }
    engines: { node: ">=8" }

  fontace@0.3.0:
    resolution:
      {
        integrity: sha512-czoqATrcnxgWb/nAkfyIrRp6Q8biYj7nGnL6zfhTcX+JKKpWHFBnb8uNMw/kZr7u++3Y3wYSYoZgHkCcsuBpBg==,
      }

  fontkit@2.0.4:
    resolution:
      {
        integrity: sha512-syetQadaUEDNdxdugga9CpEYVaQIxOwk7GlwZWWZ19//qW4zE5bknOKeMBDYAASwnpaSHKJITRLMF9m1fp3s6g==,
      }

  foreground-child@3.3.1:
    resolution:
      {
        integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==,
      }
    engines: { node: ">=14" }

  framer-motion@12.20.1:
    resolution:
      {
        integrity: sha512-NW2t2GHQcNvLHq18JyNVY15VKrwru+nkNyhLdqf4MbxbGhxZcSDi68iNcAy6O1nG0yYAQJbLioBIH1Kmg8Xr1g==,
      }
    peerDependencies:
      "@emotion/is-prop-valid": "*"
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      "@emotion/is-prop-valid":
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fs-constants@1.0.0:
    resolution:
      {
        integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==,
      }

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
      }

  gensync@1.0.0-beta.2:
    resolution:
      {
        integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
      }
    engines: { node: ">=6.9.0" }

  get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
      }
    engines: { node: 6.* || 8.* || >= 10.* }

  get-east-asian-width@1.3.0:
    resolution:
      {
        integrity: sha512-vpeMIQKxczTD/0s2CdEWHcb0eeJe6TFjxb+J5xgX7hScxqrGuyjmv4c1D4A/gelKfyox0gJJwIHF+fLjeaM8kQ==,
      }
    engines: { node: ">=18" }

  get-nonce@1.0.1:
    resolution:
      {
        integrity: sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==,
      }
    engines: { node: ">=6" }

  get-tsconfig@4.10.1:
    resolution:
      {
        integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==,
      }

  github-from-package@0.0.0:
    resolution:
      {
        integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==,
      }

  github-slugger@2.0.0:
    resolution:
      {
        integrity: sha512-IaOQ9puYtjrkq7Y0Ygl9KDZnrf/aiUJYUpVf89y8kyaxbRG7Y1SrX/jaumrv81vc61+kiMempujsM3Yw7w5qcw==,
      }

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
      }
    engines: { node: ">= 6" }

  glob-parent@6.0.2:
    resolution:
      {
        integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==,
      }
    engines: { node: ">=10.13.0" }

  glob@10.4.5:
    resolution:
      {
        integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==,
      }
    hasBin: true

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
      }
    engines: { node: ">=4" }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
      }

  h3@1.15.3:
    resolution:
      {
        integrity: sha512-z6GknHqyX0h9aQaTx22VZDf6QyZn+0Nh+Ym8O/u0SGSkyF5cuTJYKlc8MkzW3Nzf9LE1ivcpmYC3FUGpywhuUQ==,
      }

  happy-dom@18.0.1:
    resolution:
      {
        integrity: sha512-qn+rKOW7KWpVTtgIUi6RVmTBZJSe2k0Db0vh1f7CWrWclkkc7/Q+FrOfkZIb2eiErLyqu5AXEzE7XthO9JVxRA==,
      }
    engines: { node: ">=20.0.0" }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
      }
    engines: { node: ">=8" }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
      }
    engines: { node: ">= 0.4" }

  hast-util-from-html@2.0.3:
    resolution:
      {
        integrity: sha512-CUSRHXyKjzHov8yKsQjGOElXy/3EKpyX56ELnkHH34vDVw1N1XSQ1ZcAvTyAPtGqLTuKP/uxM+aLkSPqF/EtMw==,
      }

  hast-util-from-parse5@8.0.3:
    resolution:
      {
        integrity: sha512-3kxEVkEKt0zvcZ3hCRYI8rqrgwtlIOFMWkbclACvjlDw8Li9S2hk/d51OI0nr/gIpdMHNepwgOKqZ/sy0Clpyg==,
      }

  hast-util-is-element@3.0.0:
    resolution:
      {
        integrity: sha512-Val9mnv2IWpLbNPqc/pUem+a7Ipj2aHacCwgNfTiK0vJKl0LF+4Ba4+v1oPHFpf3bLYmreq0/l3Gud9S5OH42g==,
      }

  hast-util-parse-selector@4.0.0:
    resolution:
      {
        integrity: sha512-wkQCkSYoOGCRKERFWcxMVMOcYE2K1AaNLU8DXS9arxnLOUEWbOXKXiJUNzEpqZ3JOKpnha3jkFrumEjVliDe7A==,
      }

  hast-util-raw@9.1.0:
    resolution:
      {
        integrity: sha512-Y8/SBAHkZGoNkpzqqfCldijcuUKh7/su31kEBp67cFY09Wy0mTRgtsLYsiIxMJxlu0f6AA5SUTbDR8K0rxnbUw==,
      }

  hast-util-to-estree@3.1.3:
    resolution:
      {
        integrity: sha512-48+B/rJWAp0jamNbAAf9M7Uf//UVqAoMmgXhBdxTDJLGKY+LRnZ99qcG+Qjl5HfMpYNzS5v4EAwVEF34LeAj7w==,
      }

  hast-util-to-html@9.0.5:
    resolution:
      {
        integrity: sha512-OguPdidb+fbHQSU4Q4ZiLKnzWo8Wwsf5bZfbvu7//a9oTYoqD/fWpe96NuHkoS9h0ccGOTe0C4NGXdtS0iObOw==,
      }

  hast-util-to-jsx-runtime@2.3.6:
    resolution:
      {
        integrity: sha512-zl6s8LwNyo1P9uw+XJGvZtdFF1GdAkOg8ujOw+4Pyb76874fLps4ueHXDhXWdk6YHQ6OgUtinliG7RsYvCbbBg==,
      }

  hast-util-to-parse5@8.0.0:
    resolution:
      {
        integrity: sha512-3KKrV5ZVI8if87DVSi1vDeByYrkGzg4mEfeu4alwgmmIeARiBLKCZS2uw5Gb6nU9x9Yufyj3iudm6i7nl52PFw==,
      }

  hast-util-to-text@4.0.2:
    resolution:
      {
        integrity: sha512-KK6y/BN8lbaq654j7JgBydev7wuNMcID54lkRav1P0CaE1e47P72AWWPiGKXTJU271ooYzcvTAn/Zt0REnvc7A==,
      }

  hast-util-whitespace@3.0.0:
    resolution:
      {
        integrity: sha512-88JUN06ipLwsnv+dVn+OIYOvAuvBMy/Qoi6O7mQHxdPXpjy+Cd6xRkWwux7DKO+4sYILtLBRIKgsdpS2gQc7qw==,
      }

  hastscript@9.0.1:
    resolution:
      {
        integrity: sha512-g7df9rMFX/SPi34tyGCyUBREQoKkapwdY/T04Qn9TDWfHhAYt4/I0gMVirzK5wEzeUqIjEB+LXC/ypb7Aqno5w==,
      }

  hex-rgb@4.3.0:
    resolution:
      {
        integrity: sha512-Ox1pJVrDCyGHMG9CFg1tmrRUMRPRsAWYc/PinY0XzJU4K7y7vjNoLKIQ7BR5UJMCxNN8EM1MNDmHWA/B3aZUuw==,
      }
    engines: { node: ">=6" }

  html-encoding-sniffer@4.0.0:
    resolution:
      {
        integrity: sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==,
      }
    engines: { node: ">=18" }

  html-escaper@2.0.2:
    resolution:
      {
        integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==,
      }

  html-escaper@3.0.3:
    resolution:
      {
        integrity: sha512-RuMffC89BOWQoY0WKGpIhn5gX3iI54O6nRA0yC124NYVtzjmFWBIiFd8M0x+ZdX0P9R4lADg1mgP8C7PxGOWuQ==,
      }

  html-void-elements@3.0.0:
    resolution:
      {
        integrity: sha512-bEqo66MRXsUGxWHV5IP0PUiAWwoEjba4VCzg0LjFJBpchPaTfyfCKTG6bc5F8ucKec3q5y6qOdGyYTSBEvhCrg==,
      }

  http-cache-semantics@4.2.0:
    resolution:
      {
        integrity: sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==,
      }

  http-proxy-agent@7.0.2:
    resolution:
      {
        integrity: sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==,
      }
    engines: { node: ">= 14" }

  https-proxy-agent@7.0.6:
    resolution:
      {
        integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==,
      }
    engines: { node: ">= 14" }

  iconv-lite@0.6.3:
    resolution:
      {
        integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==,
      }
    engines: { node: ">=0.10.0" }

  ieee754@1.2.1:
    resolution:
      {
        integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==,
      }

  immediate@3.0.6:
    resolution:
      {
        integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==,
      }

  import-meta-resolve@4.1.0:
    resolution:
      {
        integrity: sha512-I6fiaX09Xivtk+THaMfAwnA3MVA5Big1WHF1Dfx9hFuvNIWpXnorlkzhcQf6ehrqQiiZECRt1poOAkPmer3ruw==,
      }

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
      }

  ini@1.3.8:
    resolution:
      {
        integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==,
      }

  inline-style-parser@0.2.4:
    resolution:
      {
        integrity: sha512-0aO8FkhNZlj/ZIbNi7Lxxr12obT7cL1moPfE4tg1LkX7LlLfC6DeX4l2ZEud1ukP9jNQyNnfzQVqwbwmAATY4Q==,
      }

  iron-webcrypto@1.2.1:
    resolution:
      {
        integrity: sha512-feOM6FaSr6rEABp/eDfVseKyTMDt+KGpeB35SkVn9Tyn0CqvVsY3EwI0v5i8nMHyJnzCIQf7nsy3p41TPkJZhg==,
      }

  is-alphabetical@2.0.1:
    resolution:
      {
        integrity: sha512-FWyyY60MeTNyeSRpkM2Iry0G9hpr7/9kD40mD/cGQEuilcZYS4okz8SN2Q6rLCJ8gbCt6fN+rC+6tMGS99LaxQ==,
      }

  is-alphanumerical@2.0.1:
    resolution:
      {
        integrity: sha512-hmbYhX/9MUMF5uh7tOXyK/n0ZvWpad5caBA17GsC6vyuCqaWliRG5K1qS9inmUhEMaOBIW7/whAnSwveW/LtZw==,
      }

  is-arrayish@0.3.2:
    resolution:
      {
        integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==,
      }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
      }
    engines: { node: ">=8" }

  is-core-module@2.16.1:
    resolution:
      {
        integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==,
      }
    engines: { node: ">= 0.4" }

  is-decimal@2.0.1:
    resolution:
      {
        integrity: sha512-AAB9hiomQs5DXWcRB1rqsxGUstbRroFOPPVAomNk/3XHR5JyEZChOyTWe2oayKnsSsr/kcGqF+z6yuH6HHpN0A==,
      }

  is-docker@3.0.0:
    resolution:
      {
        integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }
    hasBin: true

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
      }
    engines: { node: ">=0.10.0" }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
      }
    engines: { node: ">=8" }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
      }
    engines: { node: ">=0.10.0" }

  is-hexadecimal@2.0.1:
    resolution:
      {
        integrity: sha512-DgZQp241c8oO6cA1SbTEWiXeoxV42vlcJxgH+B3hi1AiqqKruZR3ZGF8In3fj4+/y/7rHvlOZLZtgJ/4ttYGZg==,
      }

  is-inside-container@1.0.0:
    resolution:
      {
        integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==,
      }
    engines: { node: ">=14.16" }
    hasBin: true

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
      }
    engines: { node: ">=0.12.0" }

  is-plain-obj@4.1.0:
    resolution:
      {
        integrity: sha512-+Pgi+vMuUNkJyExiMBt5IlFoMyKnr5zhJ4Uspz58WOhBF5QoIZkFyNHIbBAtHwzVAgk5RtndVNsDRN61/mmDqg==,
      }
    engines: { node: ">=12" }

  is-potential-custom-element-name@1.0.1:
    resolution:
      {
        integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==,
      }

  is-wsl@3.1.0:
    resolution:
      {
        integrity: sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==,
      }
    engines: { node: ">=16" }

  isarray@1.0.0:
    resolution:
      {
        integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==,
      }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
      }

  istanbul-lib-coverage@3.2.2:
    resolution:
      {
        integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-report@3.0.1:
    resolution:
      {
        integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==,
      }
    engines: { node: ">=10" }

  istanbul-lib-source-maps@5.0.6:
    resolution:
      {
        integrity: sha512-yg2d+Em4KizZC5niWhQaIomgf5WlL4vOOjZ5xGCmF8SnPE/mDWWXgvRExdcpCgh9lLRRa1/fSYp2ymmbJ1pI+A==,
      }
    engines: { node: ">=10" }

  istanbul-reports@3.1.7:
    resolution:
      {
        integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==,
      }
    engines: { node: ">=8" }

  jackspeak@3.4.3:
    resolution:
      {
        integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==,
      }

  jiti@1.21.7:
    resolution:
      {
        integrity: sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==,
      }
    hasBin: true

  jiti@2.4.2:
    resolution:
      {
        integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==,
      }
    hasBin: true

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
      }

  js-tokens@9.0.1:
    resolution:
      {
        integrity: sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==,
      }

  js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
      }
    hasBin: true

  jsdom@26.1.0:
    resolution:
      {
        integrity: sha512-Cvc9WUhxSMEo4McES3P7oK3QaXldCfNWp7pl2NNeiIFlCoLr3kfq9kb1fxftiwk1FLV7CvpvDfonxtzUDeSOPg==,
      }
    engines: { node: ">=18" }
    peerDependencies:
      canvas: ^3.0.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.1.0:
    resolution:
      {
        integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==,
      }
    engines: { node: ">=6" }
    hasBin: true

  json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==,
      }

  json5@2.2.3:
    resolution:
      {
        integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
      }
    engines: { node: ">=6" }
    hasBin: true

  jsonc-parser@2.3.1:
    resolution:
      {
        integrity: sha512-H8jvkz1O50L3dMZCsLqiuB2tA7muqbSg1AtGEkN0leAqGjsUzDJir3Zwr02BhqdcITPg3ei3mZ+HjMocAknhhg==,
      }

  jsonc-parser@3.3.1:
    resolution:
      {
        integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==,
      }

  jszip@3.10.1:
    resolution:
      {
        integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==,
      }

  kleur@3.0.3:
    resolution:
      {
        integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==,
      }
    engines: { node: ">=6" }

  kleur@4.1.5:
    resolution:
      {
        integrity: sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==,
      }
    engines: { node: ">=6" }

  libheif-js@1.19.8:
    resolution:
      {
        integrity: sha512-vQJWusIxO7wavpON1dusciL8Go9jsIQ+EUrckauFYAiSTjcmLAsuJh3SszLpvkwPci3JcL41ek2n+LUZGFpPIQ==,
      }
    engines: { node: ">=8.0.0" }

  lie@3.3.0:
    resolution:
      {
        integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==,
      }

  lightningcss-darwin-arm64@1.30.1:
    resolution:
      {
        integrity: sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution:
      {
        integrity: sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution:
      {
        integrity: sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution:
      {
        integrity: sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution:
      {
        integrity: sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution:
      {
        integrity: sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution:
      {
        integrity: sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.30.1:
    resolution:
      {
        integrity: sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution:
      {
        integrity: sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution:
      {
        integrity: sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==,
      }
    engines: { node: ">= 12.0.0" }
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution:
      {
        integrity: sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==,
      }
    engines: { node: ">= 12.0.0" }

  lilconfig@2.1.0:
    resolution:
      {
        integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==,
      }
    engines: { node: ">=10" }

  lilconfig@3.1.3:
    resolution:
      {
        integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==,
      }
    engines: { node: ">=14" }

  linebreak@1.1.0:
    resolution:
      {
        integrity: sha512-MHp03UImeVhB7XZtjd0E4n6+3xr5Dq/9xI/5FptGk5FrbDR3zagPa2DS6U8ks/3HjbKWG9Q1M2ufOzxV2qLYSQ==,
      }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
      }

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==,
      }
    engines: { node: ">=8" }

  lodash@4.17.21:
    resolution:
      {
        integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==,
      }

  longest-streak@3.1.0:
    resolution:
      {
        integrity: sha512-9Ri+o0JYgehTaVBBDoMqIl8GXtbWg711O3srftcHhZ0dqnETqLaoIK0x17fUw9rFSlK/0NlsKe0Ahhyl5pXE2g==,
      }

  loupe@3.1.4:
    resolution:
      {
        integrity: sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==,
      }

  lru-cache@10.4.3:
    resolution:
      {
        integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==,
      }

  lru-cache@5.1.1:
    resolution:
      {
        integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
      }

  lucide-react@0.525.0:
    resolution:
      {
        integrity: sha512-Tm1txJ2OkymCGkvwoHt33Y2JpN5xucVq1slHcgE6Lk0WjDfjgKWor5CdVER8U6DvcfMwh4M8XxmpTiyzfmfDYQ==,
      }
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  magic-string@0.30.17:
    resolution:
      {
        integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==,
      }

  magicast@0.3.5:
    resolution:
      {
        integrity: sha512-L0WhttDl+2BOsybvEOLK7fW3UA0OQ0IQ2d6Zl2x/a6vVRs3bAY0ECOSHHeL5jD+SbOpOCUEi0y1DgHEn9Qn1AQ==,
      }

  make-dir@4.0.0:
    resolution:
      {
        integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==,
      }
    engines: { node: ">=10" }

  markdown-extensions@2.0.0:
    resolution:
      {
        integrity: sha512-o5vL7aDWatOTX8LzaS1WMoaoxIiLRQJuIKKe2wAw6IeULDHaqbiqiggmx+pKvZDb1Sj+pE46Sn1T7lCqfFtg1Q==,
      }
    engines: { node: ">=16" }

  markdown-table@3.0.4:
    resolution:
      {
        integrity: sha512-wiYz4+JrLyb/DqW2hkFJxP7Vd7JuTDm77fvbM8VfEQdmSMqcImWeeRbHwZjBjIFki/VaMK2BhFi7oUUZeM5bqw==,
      }

  mdast-util-definitions@6.0.0:
    resolution:
      {
        integrity: sha512-scTllyX6pnYNZH/AIp/0ePz6s4cZtARxImwoPJ7kS42n+MnVsI4XbnG6d4ibehRIldYMWM2LD7ImQblVhUejVQ==,
      }

  mdast-util-find-and-replace@3.0.2:
    resolution:
      {
        integrity: sha512-Tmd1Vg/m3Xz43afeNxDIhWRtFZgM2VLyaf4vSTYwudTyeuTneoL3qtWMA5jeLyz/O1vDJmmV4QuScFCA2tBPwg==,
      }

  mdast-util-from-markdown@2.0.2:
    resolution:
      {
        integrity: sha512-uZhTV/8NBuw0WHkPTrCqDOl0zVe1BIng5ZtHoDk49ME1qqcjYmmLmOf0gELgcRMxN4w2iuIeVso5/6QymSrgmA==,
      }

  mdast-util-gfm-autolink-literal@2.0.1:
    resolution:
      {
        integrity: sha512-5HVP2MKaP6L+G6YaxPNjuL0BPrq9orG3TsrZ9YXbA3vDw/ACI4MEsnoDpn6ZNm7GnZgtAcONJyPhOP8tNJQavQ==,
      }

  mdast-util-gfm-footnote@2.1.0:
    resolution:
      {
        integrity: sha512-sqpDWlsHn7Ac9GNZQMeUzPQSMzR6Wv0WKRNvQRg0KqHh02fpTz69Qc1QSseNX29bhz1ROIyNyxExfawVKTm1GQ==,
      }

  mdast-util-gfm-strikethrough@2.0.0:
    resolution:
      {
        integrity: sha512-mKKb915TF+OC5ptj5bJ7WFRPdYtuHv0yTRxK2tJvi+BDqbkiG7h7u/9SI89nRAYcmap2xHQL9D+QG/6wSrTtXg==,
      }

  mdast-util-gfm-table@2.0.0:
    resolution:
      {
        integrity: sha512-78UEvebzz/rJIxLvE7ZtDd/vIQ0RHv+3Mh5DR96p7cS7HsBhYIICDBCu8csTNWNO6tBWfqXPWekRuj2FNOGOZg==,
      }

  mdast-util-gfm-task-list-item@2.0.0:
    resolution:
      {
        integrity: sha512-IrtvNvjxC1o06taBAVJznEnkiHxLFTzgonUdy8hzFVeDun0uTjxxrRGVaNFqkU1wJR3RBPEfsxmU6jDWPofrTQ==,
      }

  mdast-util-gfm@3.1.0:
    resolution:
      {
        integrity: sha512-0ulfdQOM3ysHhCJ1p06l0b0VKlhU0wuQs3thxZQagjcjPrlFRqY215uZGHHJan9GEAXd9MbfPjFJz+qMkVR6zQ==,
      }

  mdast-util-mdx-expression@2.0.1:
    resolution:
      {
        integrity: sha512-J6f+9hUp+ldTZqKRSg7Vw5V6MqjATc+3E4gf3CFNcuZNWD8XdyI6zQ8GqH7f8169MM6P7hMBRDVGnn7oHB9kXQ==,
      }

  mdast-util-mdx-jsx@3.2.0:
    resolution:
      {
        integrity: sha512-lj/z8v0r6ZtsN/cGNNtemmmfoLAFZnjMbNyLzBafjzikOM+glrjNHPlf6lQDOTccj9n5b0PPihEBbhneMyGs1Q==,
      }

  mdast-util-mdx@3.0.0:
    resolution:
      {
        integrity: sha512-JfbYLAW7XnYTTbUsmpu0kdBUVe+yKVJZBItEjwyYJiDJuZ9w4eeaqks4HQO+R7objWgS2ymV60GYpI14Ug554w==,
      }

  mdast-util-mdxjs-esm@2.0.1:
    resolution:
      {
        integrity: sha512-EcmOpxsZ96CvlP03NghtH1EsLtr0n9Tm4lPUJUBccV9RwUOneqSycg19n5HGzCf+10LozMRSObtVr3ee1WoHtg==,
      }

  mdast-util-phrasing@4.1.0:
    resolution:
      {
        integrity: sha512-TqICwyvJJpBwvGAMZjj4J2n0X8QWp21b9l0o7eXyVJ25YNWYbJDVIyD1bZXE6WtV6RmKJVYmQAKWa0zWOABz2w==,
      }

  mdast-util-to-hast@13.2.0:
    resolution:
      {
        integrity: sha512-QGYKEuUsYT9ykKBCMOEDLsU5JRObWQusAolFMeko/tYPufNkRffBAQjIE+99jbA87xv6FgmjLtwjh9wBWajwAA==,
      }

  mdast-util-to-markdown@2.1.2:
    resolution:
      {
        integrity: sha512-xj68wMTvGXVOKonmog6LwyJKrYXZPvlwabaryTjLh9LuvovB/KAH+kvi8Gjj+7rJjsFi23nkUxRQv1KqSroMqA==,
      }

  mdast-util-to-string@4.0.0:
    resolution:
      {
        integrity: sha512-0H44vDimn51F0YwvxSJSm0eCDOJTRlmN0R1yBh4HLj9wiV1Dn0QoXGbvFAWj2hSItVTlCmBF1hqKlIyUBVFLPg==,
      }

  mdn-data@2.12.2:
    resolution:
      {
        integrity: sha512-IEn+pegP1aManZuckezWCO+XZQDplx1366JoVhTpMpBB1sPey/SbveZQUosKiKiGYjg1wH4pMlNgXbCiYgihQA==,
      }

  memoize-one@5.2.1:
    resolution:
      {
        integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==,
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
      }
    engines: { node: ">= 8" }

  micromark-core-commonmark@2.0.3:
    resolution:
      {
        integrity: sha512-RDBrHEMSxVFLg6xvnXmb1Ayr2WzLAWjeSATAoxwKYJV94TeNavgoIdA0a9ytzDSVzBy2YKFK+emCPOEibLeCrg==,
      }

  micromark-extension-gfm-autolink-literal@2.1.0:
    resolution:
      {
        integrity: sha512-oOg7knzhicgQ3t4QCjCWgTmfNhvQbDDnJeVu9v81r7NltNCVmhPy1fJRX27pISafdjL+SVc4d3l48Gb6pbRypw==,
      }

  micromark-extension-gfm-footnote@2.1.0:
    resolution:
      {
        integrity: sha512-/yPhxI1ntnDNsiHtzLKYnE3vf9JZ6cAisqVDauhp4CEHxlb4uoOTxOCJ+9s51bIB8U1N1FJ1RXOKTIlD5B/gqw==,
      }

  micromark-extension-gfm-strikethrough@2.1.0:
    resolution:
      {
        integrity: sha512-ADVjpOOkjz1hhkZLlBiYA9cR2Anf8F4HqZUO6e5eDcPQd0Txw5fxLzzxnEkSkfnD0wziSGiv7sYhk/ktvbf1uw==,
      }

  micromark-extension-gfm-table@2.1.1:
    resolution:
      {
        integrity: sha512-t2OU/dXXioARrC6yWfJ4hqB7rct14e8f7m0cbI5hUmDyyIlwv5vEtooptH8INkbLzOatzKuVbQmAYcbWoyz6Dg==,
      }

  micromark-extension-gfm-tagfilter@2.0.0:
    resolution:
      {
        integrity: sha512-xHlTOmuCSotIA8TW1mDIM6X2O1SiX5P9IuDtqGonFhEK0qgRI4yeC6vMxEV2dgyr2TiD+2PQ10o+cOhdVAcwfg==,
      }

  micromark-extension-gfm-task-list-item@2.1.0:
    resolution:
      {
        integrity: sha512-qIBZhqxqI6fjLDYFTBIa4eivDMnP+OZqsNwmQ3xNLE4Cxwc+zfQEfbs6tzAo2Hjq+bh6q5F+Z8/cksrLFYWQQw==,
      }

  micromark-extension-gfm@3.0.0:
    resolution:
      {
        integrity: sha512-vsKArQsicm7t0z2GugkCKtZehqUm31oeGBV/KVSorWSy8ZlNAv7ytjFhvaryUiCUJYqs+NoE6AFhpQvBTM6Q4w==,
      }

  micromark-extension-mdx-expression@3.0.1:
    resolution:
      {
        integrity: sha512-dD/ADLJ1AeMvSAKBwO22zG22N4ybhe7kFIZ3LsDI0GlsNr2A3KYxb0LdC1u5rj4Nw+CHKY0RVdnHX8vj8ejm4Q==,
      }

  micromark-extension-mdx-jsx@3.0.2:
    resolution:
      {
        integrity: sha512-e5+q1DjMh62LZAJOnDraSSbDMvGJ8x3cbjygy2qFEi7HCeUT4BDKCvMozPozcD6WmOt6sVvYDNBKhFSz3kjOVQ==,
      }

  micromark-extension-mdx-md@2.0.0:
    resolution:
      {
        integrity: sha512-EpAiszsB3blw4Rpba7xTOUptcFeBFi+6PY8VnJ2hhimH+vCQDirWgsMpz7w1XcZE7LVrSAUGb9VJpG9ghlYvYQ==,
      }

  micromark-extension-mdxjs-esm@3.0.0:
    resolution:
      {
        integrity: sha512-DJFl4ZqkErRpq/dAPyeWp15tGrcrrJho1hKK5uBS70BCtfrIFg81sqcTVu3Ta+KD1Tk5vAtBNElWxtAa+m8K9A==,
      }

  micromark-extension-mdxjs@3.0.0:
    resolution:
      {
        integrity: sha512-A873fJfhnJ2siZyUrJ31l34Uqwy4xIFmvPY1oj+Ean5PHcPBYzEsvqvWGaWcfEIr11O5Dlw3p2y0tZWpKHDejQ==,
      }

  micromark-factory-destination@2.0.1:
    resolution:
      {
        integrity: sha512-Xe6rDdJlkmbFRExpTOmRj9N3MaWmbAgdpSrBQvCFqhezUn4AHqJHbaEnfbVYYiexVSs//tqOdY/DxhjdCiJnIA==,
      }

  micromark-factory-label@2.0.1:
    resolution:
      {
        integrity: sha512-VFMekyQExqIW7xIChcXn4ok29YE3rnuyveW3wZQWWqF4Nv9Wk5rgJ99KzPvHjkmPXF93FXIbBp6YdW3t71/7Vg==,
      }

  micromark-factory-mdx-expression@2.0.3:
    resolution:
      {
        integrity: sha512-kQnEtA3vzucU2BkrIa8/VaSAsP+EJ3CKOvhMuJgOEGg9KDC6OAY6nSnNDVRiVNRqj7Y4SlSzcStaH/5jge8JdQ==,
      }

  micromark-factory-space@2.0.1:
    resolution:
      {
        integrity: sha512-zRkxjtBxxLd2Sc0d+fbnEunsTj46SWXgXciZmHq0kDYGnck/ZSGj9/wULTV95uoeYiK5hRXP2mJ98Uo4cq/LQg==,
      }

  micromark-factory-title@2.0.1:
    resolution:
      {
        integrity: sha512-5bZ+3CjhAd9eChYTHsjy6TGxpOFSKgKKJPJxr293jTbfry2KDoWkhBb6TcPVB4NmzaPhMs1Frm9AZH7OD4Cjzw==,
      }

  micromark-factory-whitespace@2.0.1:
    resolution:
      {
        integrity: sha512-Ob0nuZ3PKt/n0hORHyvoD9uZhr+Za8sFoP+OnMcnWK5lngSzALgQYKMr9RJVOWLqQYuyn6ulqGWSXdwf6F80lQ==,
      }

  micromark-util-character@2.1.1:
    resolution:
      {
        integrity: sha512-wv8tdUTJ3thSFFFJKtpYKOYiGP2+v96Hvk4Tu8KpCAsTMs6yi+nVmGh1syvSCsaxz45J6Jbw+9DD6g97+NV67Q==,
      }

  micromark-util-chunked@2.0.1:
    resolution:
      {
        integrity: sha512-QUNFEOPELfmvv+4xiNg2sRYeS/P84pTW0TCgP5zc9FpXetHY0ab7SxKyAQCNCc1eK0459uoLI1y5oO5Vc1dbhA==,
      }

  micromark-util-classify-character@2.0.1:
    resolution:
      {
        integrity: sha512-K0kHzM6afW/MbeWYWLjoHQv1sgg2Q9EccHEDzSkxiP/EaagNzCm7T/WMKZ3rjMbvIpvBiZgwR3dKMygtA4mG1Q==,
      }

  micromark-util-combine-extensions@2.0.1:
    resolution:
      {
        integrity: sha512-OnAnH8Ujmy59JcyZw8JSbK9cGpdVY44NKgSM7E9Eh7DiLS2E9RNQf0dONaGDzEG9yjEl5hcqeIsj4hfRkLH/Bg==,
      }

  micromark-util-decode-numeric-character-reference@2.0.2:
    resolution:
      {
        integrity: sha512-ccUbYk6CwVdkmCQMyr64dXz42EfHGkPQlBj5p7YVGzq8I7CtjXZJrubAYezf7Rp+bjPseiROqe7G6foFd+lEuw==,
      }

  micromark-util-decode-string@2.0.1:
    resolution:
      {
        integrity: sha512-nDV/77Fj6eH1ynwscYTOsbK7rR//Uj0bZXBwJZRfaLEJ1iGBR6kIfNmlNqaqJf649EP0F3NWNdeJi03elllNUQ==,
      }

  micromark-util-encode@2.0.1:
    resolution:
      {
        integrity: sha512-c3cVx2y4KqUnwopcO9b/SCdo2O67LwJJ/UyqGfbigahfegL9myoEFoDYZgkT7f36T0bLrM9hZTAaAyH+PCAXjw==,
      }

  micromark-util-events-to-acorn@2.0.3:
    resolution:
      {
        integrity: sha512-jmsiEIiZ1n7X1Rr5k8wVExBQCg5jy4UXVADItHmNk1zkwEVhBuIUKRu3fqv+hs4nxLISi2DQGlqIOGiFxgbfHg==,
      }

  micromark-util-html-tag-name@2.0.1:
    resolution:
      {
        integrity: sha512-2cNEiYDhCWKI+Gs9T0Tiysk136SnR13hhO8yW6BGNyhOC4qYFnwF1nKfD3HFAIXA5c45RrIG1ub11GiXeYd1xA==,
      }

  micromark-util-normalize-identifier@2.0.1:
    resolution:
      {
        integrity: sha512-sxPqmo70LyARJs0w2UclACPUUEqltCkJ6PhKdMIDuJ3gSf/Q+/GIe3WKl0Ijb/GyH9lOpUkRAO2wp0GVkLvS9Q==,
      }

  micromark-util-resolve-all@2.0.1:
    resolution:
      {
        integrity: sha512-VdQyxFWFT2/FGJgwQnJYbe1jjQoNTS4RjglmSjTUlpUMa95Htx9NHeYW4rGDJzbjvCsl9eLjMQwGeElsqmzcHg==,
      }

  micromark-util-sanitize-uri@2.0.1:
    resolution:
      {
        integrity: sha512-9N9IomZ/YuGGZZmQec1MbgxtlgougxTodVwDzzEouPKo3qFWvymFHWcnDi2vzV1ff6kas9ucW+o3yzJK9YB1AQ==,
      }

  micromark-util-subtokenize@2.1.0:
    resolution:
      {
        integrity: sha512-XQLu552iSctvnEcgXw6+Sx75GflAPNED1qx7eBJ+wydBb2KCbRZe+NwvIEEMM83uml1+2WSXpBAcp9IUCgCYWA==,
      }

  micromark-util-symbol@2.0.1:
    resolution:
      {
        integrity: sha512-vs5t8Apaud9N28kgCrRUdEed4UJ+wWNvicHLPxCa9ENlYuAY31M0ETy5y1vA33YoNPDFTghEbnh6efaE8h4x0Q==,
      }

  micromark-util-types@2.0.2:
    resolution:
      {
        integrity: sha512-Yw0ECSpJoViF1qTU4DC6NwtC4aWGt1EkzaQB8KPPyCRR8z9TWeV0HbEFGTO+ZY1wB22zmxnJqhPyTpOVCpeHTA==,
      }

  micromark@4.0.2:
    resolution:
      {
        integrity: sha512-zpe98Q6kvavpCr1NPVSCMebCKfD7CA2NqZ+rykeNhONIJBpc1tFKt9hucLGwha3jNTNI8lHpctWJWoimVF4PfA==,
      }

  micromatch@4.0.8:
    resolution:
      {
        integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==,
      }
    engines: { node: ">=8.6" }

  mimic-response@3.1.0:
    resolution:
      {
        integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==,
      }
    engines: { node: ">=10" }

  minimatch@9.0.5:
    resolution:
      {
        integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
      }

  minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minizlib@3.0.2:
    resolution:
      {
        integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==,
      }
    engines: { node: ">= 18" }

  mkdirp-classic@0.5.3:
    resolution:
      {
        integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==,
      }

  mkdirp@3.0.1:
    resolution:
      {
        integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==,
      }
    engines: { node: ">=10" }
    hasBin: true

  motion-dom@12.20.1:
    resolution:
      {
        integrity: sha512-XyveLJ9dmQTmaEsP9RlcuoNFxWlRIGdasdPJBB4aOwPr8bRcJdhltudAbiEjRQBmsGD30sjJdaEjhkHsAHapLQ==,
      }

  motion-utils@12.19.0:
    resolution:
      {
        integrity: sha512-BuFTHINYmV07pdWs6lj6aI63vr2N4dg0vR+td0rtrdpWOhBzIkEklZyLcvKBoEtwSqx8Jg06vUB5RS0xDiUybw==,
      }

  mrmime@2.0.1:
    resolution:
      {
        integrity: sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==,
      }
    engines: { node: ">=10" }

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
      }

  muggle-string@0.4.1:
    resolution:
      {
        integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==,
      }

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
      }

  nanoid@3.3.11:
    resolution:
      {
        integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  napi-build-utils@2.0.0:
    resolution:
      {
        integrity: sha512-GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA==,
      }

  neotraverse@0.6.18:
    resolution:
      {
        integrity: sha512-Z4SmBUweYa09+o6pG+eASabEpP6QkQ70yHj351pQoEXIs8uHbaU2DWVmzBANKgflPa47A50PtB2+NgRpQvr7vA==,
      }
    engines: { node: ">= 10" }

  next-themes@0.4.6:
    resolution:
      {
        integrity: sha512-pZvgD5L0IEvX5/9GWyHMf3m8BKiVQwsCMHfoFosXtXBMnaS0ZnIJ9ST4b4NqLVKDEm8QBxoNNGNaBv2JNF6XNA==,
      }
    peerDependencies:
      react: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc
      react-dom: ^16.8 || ^17 || ^18 || ^19 || ^19.0.0-rc

  nlcst-to-string@4.0.0:
    resolution:
      {
        integrity: sha512-YKLBCcUYKAg0FNlOBT6aI91qFmSiFKiluk655WzPF+DDMA02qIyy8uiRqI8QXtcFpEvll12LpL5MXqEmAZ+dcA==,
      }

  node-abi@3.75.0:
    resolution:
      {
        integrity: sha512-OhYaY5sDsIka7H7AtijtI9jwGYLyl29eQn/W623DiN/MIv5sUqc4g7BIDThX+gb7di9f6xK02nkp8sdfFWZLTg==,
      }
    engines: { node: ">=10" }

  node-addon-api@7.1.1:
    resolution:
      {
        integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==,
      }

  node-fetch-native@1.6.6:
    resolution:
      {
        integrity: sha512-8Mc2HhqPdlIfedsuZoc3yioPuzp6b+L5jRCRY1QzuWZh2EGJVQrGppC6V6cF0bLdbW0+O2YpqCA25aF/1lvipQ==,
      }

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-mock-http@1.0.0:
    resolution:
      {
        integrity: sha512-0uGYQ1WQL1M5kKvGRXWQ3uZCHtLTO8hln3oBjIusM75WoesZ909uQJs/Hb946i2SS+Gsrhkaa6iAO17jRIv6DQ==,
      }

  node-releases@2.0.19:
    resolution:
      {
        integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==,
      }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
      }
    engines: { node: ">=0.10.0" }

  nwsapi@2.2.20:
    resolution:
      {
        integrity: sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==,
      }

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
      }
    engines: { node: ">=0.10.0" }

  object-hash@3.0.0:
    resolution:
      {
        integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==,
      }
    engines: { node: ">= 6" }

  ofetch@1.4.1:
    resolution:
      {
        integrity: sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==,
      }

  ohash@2.0.11:
    resolution:
      {
        integrity: sha512-RdR9FQrFwNBNXAr4GixM8YaRZRJ5PUWbKYbE5eOsrwAjJW0q2REGcf79oYPsLyskQCZG1PLN+S/K1V00joZAoQ==,
      }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
      }

  oniguruma-parser@0.12.1:
    resolution:
      {
        integrity: sha512-8Unqkvk1RYc6yq2WBYRj4hdnsAxVze8i7iPfQr8e4uSP3tRv0rpZcbGUDvxfQQcdwHt/e9PrMvGCsa8OqG9X3w==,
      }

  oniguruma-to-es@4.3.3:
    resolution:
      {
        integrity: sha512-rPiZhzC3wXwE59YQMRDodUwwT9FZ9nNBwQQfsd1wfdtlKEyCdRV0avrTcSZ5xlIvGRVPd/cx6ZN45ECmS39xvg==,
      }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==,
      }
    engines: { node: ">=6" }

  p-limit@6.2.0:
    resolution:
      {
        integrity: sha512-kuUqqHNUqoIWp/c467RI4X6mmyuojY5jGutNU0wVTmEOOfcuwLqyMVoAi9MKi2Ak+5i9+nhmrK4ufZE8069kHA==,
      }
    engines: { node: ">=18" }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==,
      }
    engines: { node: ">=8" }

  p-queue@8.1.0:
    resolution:
      {
        integrity: sha512-mxLDbbGIBEXTJL0zEx8JIylaj3xQ7Z/7eEVjcF9fJX4DBiH9oqe+oahYnlKKxm0Ci9TlWTyhSHgygxMxjIB2jw==,
      }
    engines: { node: ">=18" }

  p-timeout@6.1.4:
    resolution:
      {
        integrity: sha512-MyIV3ZA/PmyBN/ud8vV9XzwTrNtR4jFrObymZYnZqMmW0zA8Z17vnT0rBgFE/TlohB+YCHqXMgZzb3Csp49vqg==,
      }
    engines: { node: ">=14.16" }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==,
      }
    engines: { node: ">=6" }

  package-json-from-dist@1.0.1:
    resolution:
      {
        integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==,
      }

  package-manager-detector@1.3.0:
    resolution:
      {
        integrity: sha512-ZsEbbZORsyHuO00lY1kV3/t72yp6Ysay6Pd17ZAlNGuGwmWDLCJxFpRs0IzfXfj1o4icJOkUEioexFHzyPurSQ==,
      }

  pako@0.2.9:
    resolution:
      {
        integrity: sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==,
      }

  pako@1.0.11:
    resolution:
      {
        integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==,
      }

  parse-css-color@0.2.1:
    resolution:
      {
        integrity: sha512-bwS/GGIFV3b6KS4uwpzCFj4w297Yl3uqnSgIPsoQkx7GMLROXfMnWvxfNkL0oh8HVhZA4hvJoEoEIqonfJ3BWg==,
      }

  parse-entities@4.0.2:
    resolution:
      {
        integrity: sha512-GG2AQYWoLgL877gQIKeRPGO1xF9+eG1ujIb5soS5gPvLQ1y2o8FL90w2QWNdf9I361Mpp7726c+lj3U0qK1uGw==,
      }

  parse-latin@7.0.0:
    resolution:
      {
        integrity: sha512-mhHgobPPua5kZ98EF4HWiH167JWBfl4pvAIXXdbaVohtK7a6YBOy56kvhCqduqyo/f3yrHFWmqmiMg/BkBkYYQ==,
      }

  parse5@7.3.0:
    resolution:
      {
        integrity: sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==,
      }

  path-browserify@1.0.1:
    resolution:
      {
        integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==,
      }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
      }
    engines: { node: ">=8" }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
      }
    engines: { node: ">=8" }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
      }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
      }
    engines: { node: ">=16 || 14 >=14.18" }

  pathe@2.0.3:
    resolution:
      {
        integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==,
      }

  pathval@2.0.0:
    resolution:
      {
        integrity: sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==,
      }
    engines: { node: ">= 14.16" }

  pdf-lib@1.17.1:
    resolution:
      {
        integrity: sha512-V/mpyJAoTsN4cnP31vc0wfNA1+p20evqqnap0KLoRUN0Yk/p3wN52DOEsL4oBFcLdb76hlpKPtzJIgo67j/XLw==,
      }

  pdfjs-dist@5.3.31:
    resolution:
      {
        integrity: sha512-EhPdIjNX0fcdwYQO+e3BAAJPXt+XI29TZWC7COhIXs/K0JHcUt1Gdz1ITpebTwVMFiLsukdUZ3u0oTO7jij+VA==,
      }
    engines: { node: ">=20.16.0 || >=22.3.0" }

  picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
      }
    engines: { node: ">=8.6" }

  picomatch@4.0.2:
    resolution:
      {
        integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==,
      }
    engines: { node: ">=12" }

  pify@2.3.0:
    resolution:
      {
        integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==,
      }
    engines: { node: ">=0.10.0" }

  pirates@4.0.7:
    resolution:
      {
        integrity: sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==,
      }
    engines: { node: ">= 6" }

  pngjs@5.0.0:
    resolution:
      {
        integrity: sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==,
      }
    engines: { node: ">=10.13.0" }

  postcss-css-variables@0.18.0:
    resolution:
      {
        integrity: sha512-lYS802gHbzn1GI+lXvy9MYIYDuGnl1WB4FTKoqMQqJ3Mab09A7a/1wZvGTkCEZJTM8mSbIyb1mJYn8f0aPye0Q==,
      }
    peerDependencies:
      postcss: ^8.2.6

  postcss-import@15.1.0:
    resolution:
      {
        integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution:
      {
        integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==,
      }
    engines: { node: ^12 || ^14 || >= 16 }
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution:
      {
        integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
      }
    engines: { node: ">= 14" }
    peerDependencies:
      postcss: ">=8.0.9"
      ts-node: ">=9.0.0"
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.2.0:
    resolution:
      {
        integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==,
      }
    engines: { node: ">=12.0" }
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.1.2:
    resolution:
      {
        integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==,
      }
    engines: { node: ">=4" }

  postcss-value-parser@4.2.0:
    resolution:
      {
        integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==,
      }

  postcss@8.4.31:
    resolution:
      {
        integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==,
      }
    engines: { node: ^10 || ^12 || >=14 }

  postcss@8.5.6:
    resolution:
      {
        integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==,
      }
    engines: { node: ^10 || ^12 || >=14 }

  prebuild-install@7.1.3:
    resolution:
      {
        integrity: sha512-8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug==,
      }
    engines: { node: ">=10" }
    hasBin: true

  prettier@2.8.7:
    resolution:
      {
        integrity: sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw==,
      }
    engines: { node: ">=10.13.0" }
    hasBin: true

  prismjs@1.30.0:
    resolution:
      {
        integrity: sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==,
      }
    engines: { node: ">=6" }

  process-nextick-args@2.0.1:
    resolution:
      {
        integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==,
      }

  prompts@2.4.2:
    resolution:
      {
        integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==,
      }
    engines: { node: ">= 6" }

  property-information@6.5.0:
    resolution:
      {
        integrity: sha512-PgTgs/BlvHxOu8QuEN7wi5A0OmXaBcHpmCSTehcs6Uuu9IkDIEo13Hy7n898RHfrQ49vKCoGeWZSaAK01nwVig==,
      }

  property-information@7.1.0:
    resolution:
      {
        integrity: sha512-TwEZ+X+yCJmYfL7TPUOcvBZ4QfoT5YenQiJuX//0th53DE6w0xxLEtfK3iyryQFddXuvkIk51EEgrJQ0WJkOmQ==,
      }

  pump@3.0.3:
    resolution:
      {
        integrity: sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==,
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
      }
    engines: { node: ">=6" }

  qrcode@1.5.4:
    resolution:
      {
        integrity: sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==,
      }
    engines: { node: ">=10.13.0" }
    hasBin: true

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
      }

  radix3@1.1.2:
    resolution:
      {
        integrity: sha512-b484I/7b8rDEdSDKckSSBA8knMpcdsXudlE/LNL639wFoHKwLbEkQFZHWEYwDC0wa0FKUcCY+GAF73Z7wxNVFA==,
      }

  rc@1.2.8:
    resolution:
      {
        integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==,
      }
    hasBin: true

  react-dom@19.1.0:
    resolution:
      {
        integrity: sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==,
      }
    peerDependencies:
      react: ^19.1.0

  react-icons@5.5.0:
    resolution:
      {
        integrity: sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw==,
      }
    peerDependencies:
      react: "*"

  react-refresh@0.17.0:
    resolution:
      {
        integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==,
      }
    engines: { node: ">=0.10.0" }

  react-remove-scroll-bar@2.3.8:
    resolution:
      {
        integrity: sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true

  react-remove-scroll@2.7.1:
    resolution:
      {
        integrity: sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  react-style-singleton@2.2.3:
    resolution:
      {
        integrity: sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  react-window@1.8.11:
    resolution:
      {
        integrity: sha512-+SRbUVT2scadgFSWx+R1P754xHPEqvcfSfVX10QYg6POOz+WNgkN48pS+BtZNIMGiL1HYrSEiCkwsMS15QogEQ==,
      }
    engines: { node: ">8.0.0" }
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  react@19.1.0:
    resolution:
      {
        integrity: sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==,
      }
    engines: { node: ">=0.10.0" }

  read-cache@1.0.0:
    resolution:
      {
        integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==,
      }

  readable-stream@2.3.8:
    resolution:
      {
        integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==,
      }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
      }
    engines: { node: ">= 6" }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
      }
    engines: { node: ">=8.10.0" }

  readdirp@4.1.2:
    resolution:
      {
        integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==,
      }
    engines: { node: ">= 14.18.0" }

  recma-build-jsx@1.0.0:
    resolution:
      {
        integrity: sha512-8GtdyqaBcDfva+GUKDr3nev3VpKAhup1+RvkMvUxURHpW7QyIvk9F5wz7Vzo06CEMSilw6uArgRqhpiUcWp8ew==,
      }

  recma-jsx@1.0.0:
    resolution:
      {
        integrity: sha512-5vwkv65qWwYxg+Atz95acp8DMu1JDSqdGkA2Of1j6rCreyFUE/gp15fC8MnGEuG1W68UKjM6x6+YTWIh7hZM/Q==,
      }

  recma-parse@1.0.0:
    resolution:
      {
        integrity: sha512-OYLsIGBB5Y5wjnSnQW6t3Xg7q3fQ7FWbw/vcXtORTnyaSFscOtABg+7Pnz6YZ6c27fG1/aN8CjfwoUEUIdwqWQ==,
      }

  recma-stringify@1.0.0:
    resolution:
      {
        integrity: sha512-cjwII1MdIIVloKvC9ErQ+OgAtwHBmcZ0Bg4ciz78FtbT8In39aAYbaA7zvxQ61xVMSPE8WxhLwLbhif4Js2C+g==,
      }

  regex-recursion@6.0.2:
    resolution:
      {
        integrity: sha512-0YCaSCq2VRIebiaUviZNs0cBz1kg5kVS2UKUfNIx8YVs1cN3AV7NTctO5FOKBA+UT2BPJIWZauYHPqJODG50cg==,
      }

  regex-utilities@2.3.0:
    resolution:
      {
        integrity: sha512-8VhliFJAWRaUiVvREIiW2NXXTmHs4vMNnSzuJVhscgmGav3g9VDxLrQndI3dZZVVdp0ZO/5v0xmX516/7M9cng==,
      }

  regex@6.0.1:
    resolution:
      {
        integrity: sha512-uorlqlzAKjKQZ5P+kTJr3eeJGSVroLKoHmquUj4zHWuR+hEyNqlXsSKlYYF5F4NI6nl7tWCs0apKJ0lmfsXAPA==,
      }

  rehype-parse@9.0.1:
    resolution:
      {
        integrity: sha512-ksCzCD0Fgfh7trPDxr2rSylbwq9iYDkSn8TCDmEJ49ljEUBxDVCzCHv7QNzZOfODanX4+bWQ4WZqLCRWYLfhag==,
      }

  rehype-raw@7.0.0:
    resolution:
      {
        integrity: sha512-/aE8hCfKlQeA8LmyeyQvQF3eBiLRGNlfBJEvWH7ivp9sBqs7TNqBL5X3v157rM4IFETqDnIOO+z5M/biZbo9Ww==,
      }

  rehype-recma@1.0.0:
    resolution:
      {
        integrity: sha512-lqA4rGUf1JmacCNWWZx0Wv1dHqMwxzsDWYMTowuplHF3xH0N/MmrZ/G3BDZnzAkRmxDadujCjaKM2hqYdCBOGw==,
      }

  rehype-stringify@10.0.1:
    resolution:
      {
        integrity: sha512-k9ecfXHmIPuFVI61B9DeLPN0qFHfawM6RsuX48hoqlaKSF61RskNjSm1lI8PhBEM0MRdLxVVm4WmTqJQccH9mA==,
      }

  rehype@13.0.2:
    resolution:
      {
        integrity: sha512-j31mdaRFrwFRUIlxGeuPXXKWQxet52RBQRvCmzl5eCefn/KGbomK5GMHNMsOJf55fgo3qw5tST5neDuarDYR2A==,
      }

  remark-gfm@4.0.1:
    resolution:
      {
        integrity: sha512-1quofZ2RQ9EWdeN34S79+KExV1764+wCUGop5CPL1WGdD0ocPpu91lzPGbwWMECpEpd42kJGQwzRfyov9j4yNg==,
      }

  remark-mdx@3.1.0:
    resolution:
      {
        integrity: sha512-Ngl/H3YXyBV9RcRNdlYsZujAmhsxwzxpDzpDEhFBVAGthS4GDgnctpDjgFl/ULx5UEDzqtW1cyBSNKqYYrqLBA==,
      }

  remark-parse@11.0.0:
    resolution:
      {
        integrity: sha512-FCxlKLNGknS5ba/1lmpYijMUzX2esxW5xQqjWxw2eHFfS2MSdaHVINFmhjo+qN1WhZhNimq0dZATN9pH0IDrpA==,
      }

  remark-rehype@11.1.2:
    resolution:
      {
        integrity: sha512-Dh7l57ianaEoIpzbp0PC9UKAdCSVklD8E5Rpw7ETfbTl3FqcOOgq5q2LVDhgGCkaBv7p24JXikPdvhhmHvKMsw==,
      }

  remark-smartypants@3.0.2:
    resolution:
      {
        integrity: sha512-ILTWeOriIluwEvPjv67v7Blgrcx+LZOkAUVtKI3putuhlZm84FnqDORNXPPm+HY3NdZOMhyDwZ1E+eZB/Df5dA==,
      }
    engines: { node: ">=16.0.0" }

  remark-stringify@11.0.0:
    resolution:
      {
        integrity: sha512-1OSmLd3awB/t8qdoEOMazZkNsfVTeY4fTsgzcQFdXNq8ToTN4ZGwrMnlda4K6smTFKD+GRV6O48i6Z4iKgPPpw==,
      }

  request-light@0.5.8:
    resolution:
      {
        integrity: sha512-3Zjgh+8b5fhRJBQZoy+zbVKpAQGLyka0MPgW3zruTF4dFFJ8Fqcfu9YsAvi/rvdcaTeWG3MkbZv4WKxAn/84Lg==,
      }

  request-light@0.7.0:
    resolution:
      {
        integrity: sha512-lMbBMrDoxgsyO+yB3sDcrDuX85yYt7sS8BfQd11jtbW/z5ZWgLZRcEGLsLoYw7I0WSUGQBs8CC8ScIxkTX1+6Q==,
      }

  require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
      }
    engines: { node: ">=0.10.0" }

  require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==,
      }
    engines: { node: ">=0.10.0" }

  require-main-filename@2.0.0:
    resolution:
      {
        integrity: sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==,
      }

  resolve-pkg-maps@1.0.0:
    resolution:
      {
        integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==,
      }

  resolve@1.22.10:
    resolution:
      {
        integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==,
      }
    engines: { node: ">= 0.4" }
    hasBin: true

  restructure@3.0.2:
    resolution:
      {
        integrity: sha512-gSfoiOEA0VPE6Tukkrr7I0RBdE0s7H1eFCDBk05l1KIQT1UIKNc5JZy6jdyW6eYH3aR3g5b3PuL77rq0hvwtAw==,
      }

  retext-latin@4.0.0:
    resolution:
      {
        integrity: sha512-hv9woG7Fy0M9IlRQloq/N6atV82NxLGveq+3H2WOi79dtIYWN8OaxogDm77f8YnVXJL2VD3bbqowu5E3EMhBYA==,
      }

  retext-smartypants@6.2.0:
    resolution:
      {
        integrity: sha512-kk0jOU7+zGv//kfjXEBjdIryL1Acl4i9XNkHxtM7Tm5lFiCog576fjNC9hjoR7LTKQ0DsPWy09JummSsH1uqfQ==,
      }

  retext-stringify@4.0.0:
    resolution:
      {
        integrity: sha512-rtfN/0o8kL1e+78+uxPTqu1Klt0yPzKuQ2BfWwwfgIUSayyzxpM1PJzkKt4V8803uB9qSy32MvI7Xep9khTpiA==,
      }

  retext@9.0.0:
    resolution:
      {
        integrity: sha512-sbMDcpHCNjvlheSgMfEcVrZko3cDzdbe1x/e7G66dFp0Ff7Mldvi2uv6JkJQzdRcvLYE8CA8Oe8siQx8ZOgTcA==,
      }

  reusify@1.1.0:
    resolution:
      {
        integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==,
      }
    engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

  rollup@4.43.0:
    resolution:
      {
        integrity: sha512-wdN2Kd3Twh8MAEOEJZsuxuLKCsBEo4PVNLK6tQWAn10VhsVewQLzcucMgLolRlhFybGxfclbPeEYBaP6RvUFGg==,
      }
    engines: { node: ">=18.0.0", npm: ">=8.0.0" }
    hasBin: true

  rrweb-cssom@0.8.0:
    resolution:
      {
        integrity: sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw==,
      }

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
      }

  safe-buffer@5.1.2:
    resolution:
      {
        integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==,
      }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
      }

  satori@0.15.2:
    resolution:
      {
        integrity: sha512-vu/49vdc8MzV5jUchs3TIRDCOkOvMc1iJ11MrZvhg9tE4ziKIEIBjBZvies6a9sfM2vQ2gc3dXeu6rCK7AztHA==,
      }
    engines: { node: ">=16" }

  sax@1.4.1:
    resolution:
      {
        integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==,
      }

  saxes@6.0.0:
    resolution:
      {
        integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==,
      }
    engines: { node: ">=v12.22.7" }

  scheduler@0.26.0:
    resolution:
      {
        integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==,
      }

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
      }
    hasBin: true

  semver@7.7.2:
    resolution:
      {
        integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==,
      }
    engines: { node: ">=10" }
    hasBin: true

  set-blocking@2.0.0:
    resolution:
      {
        integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==,
      }

  setimmediate@1.0.5:
    resolution:
      {
        integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==,
      }

  sharp@0.33.5:
    resolution:
      {
        integrity: sha512-haPVm1EkS9pgvHrQ/F3Xy+hgcuMV0Wm9vfIBSiwZ05k+xgb0PkBQpGsAA/oWdDobNaZTH5ppvHtzCFbnSEwHVw==,
      }
    engines: { node: ^18.17.0 || ^20.3.0 || >=21.0.0 }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
      }
    engines: { node: ">=8" }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
      }
    engines: { node: ">=8" }

  shiki@3.7.0:
    resolution:
      {
        integrity: sha512-ZcI4UT9n6N2pDuM2n3Jbk0sR4Swzq43nLPgS/4h0E3B/NrFn2HKElrDtceSf8Zx/OWYOo7G1SAtBLypCp+YXqg==,
      }

  siginfo@2.0.0:
    resolution:
      {
        integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==,
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
      }
    engines: { node: ">=14" }

  simple-concat@1.0.1:
    resolution:
      {
        integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==,
      }

  simple-get@4.0.1:
    resolution:
      {
        integrity: sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==,
      }

  simple-swizzle@0.2.2:
    resolution:
      {
        integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==,
      }

  sisteransi@1.0.5:
    resolution:
      {
        integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==,
      }

  sitemap@8.0.0:
    resolution:
      {
        integrity: sha512-+AbdxhM9kJsHtruUF39bwS/B0Fytw6Fr1o4ZAIAEqA6cke2xcoO2GleBw9Zw7nRzILVEgz7zBM5GiTJjie1G9A==,
      }
    engines: { node: ">=14.0.0", npm: ">=6.0.0" }
    hasBin: true

  smol-toml@1.3.4:
    resolution:
      {
        integrity: sha512-UOPtVuYkzYGee0Bd2Szz8d2G3RfMfJ2t3qVdZUAozZyAk+a0Sxa+QKix0YCwjL/A1RR0ar44nCxaoN9FxdJGwA==,
      }
    engines: { node: ">= 18" }

  sonner@2.0.5:
    resolution:
      {
        integrity: sha512-YwbHQO6cSso3HBXlbCkgrgzDNIhws14r4MO87Ofy+cV2X7ES4pOoAK3+veSmVTvqNx1BWUxlhPmZzP00Crk2aQ==,
      }
    peerDependencies:
      react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
      react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc

  source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
      }
    engines: { node: ">=0.10.0" }

  source-map@0.7.4:
    resolution:
      {
        integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==,
      }
    engines: { node: ">= 8" }

  space-separated-tokens@2.0.2:
    resolution:
      {
        integrity: sha512-PEGlAwrG8yXGXRjW32fGbg66JAlOAwbObuqVoJpv/mRgoWDQfgH1wDPvtzWyUSNAXBGSk8h755YDbbcEy3SH2Q==,
      }

  stackback@0.0.2:
    resolution:
      {
        integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==,
      }

  std-env@3.9.0:
    resolution:
      {
        integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==,
      }

  stream-replace-string@2.0.0:
    resolution:
      {
        integrity: sha512-TlnjJ1C0QrmxRNrON00JvaFFlNh5TTG00APw23j74ET7gkQpTASi6/L2fuiav8pzK715HXtUeClpBTw2NPSn6w==,
      }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
      }
    engines: { node: ">=8" }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
      }
    engines: { node: ">=12" }

  string-width@7.2.0:
    resolution:
      {
        integrity: sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==,
      }
    engines: { node: ">=18" }

  string.prototype.codepointat@0.2.1:
    resolution:
      {
        integrity: sha512-2cBVCj6I4IOvEnjgO/hWqXjqBGsY+zwPmHl12Srk9IXSZ56Jwwmy+66XO5Iut/oQVR7t5ihYdLB0GMa4alEUcg==,
      }

  string_decoder@1.1.1:
    resolution:
      {
        integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==,
      }

  stringify-entities@4.0.4:
    resolution:
      {
        integrity: sha512-IwfBptatlO+QCJUo19AqvrPNqlVMpW9YEL2LIVY+Rpv2qsjCGxaDLNRgeGsQWJhfItebuJhsGSLjaBbNSQ+ieg==,
      }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
      }
    engines: { node: ">=8" }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
      }
    engines: { node: ">=12" }

  strip-json-comments@2.0.1:
    resolution:
      {
        integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==,
      }
    engines: { node: ">=0.10.0" }

  strip-literal@3.0.0:
    resolution:
      {
        integrity: sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==,
      }

  style-to-js@1.1.17:
    resolution:
      {
        integrity: sha512-xQcBGDxJb6jjFCTzvQtfiPn6YvvP2O8U1MDIPNfJQlWMYfktPy+iGsHE7cssjs7y84d9fQaK4UF3RIJaAHSoYA==,
      }

  style-to-object@1.0.9:
    resolution:
      {
        integrity: sha512-G4qppLgKu/k6FwRpHiGiKPaPTFcG3g4wNVX/Qsfu+RqQM30E7Tyu/TEgxcL9PNLF5pdRLwQdE3YKKf+KF2Dzlw==,
      }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    hasBin: true

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
      }
    engines: { node: ">=8" }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
      }
    engines: { node: ">= 0.4" }

  symbol-tree@3.2.4:
    resolution:
      {
        integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==,
      }

  tailwind-merge@3.3.1:
    resolution:
      {
        integrity: sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g==,
      }

  tailwindcss@3.3.2:
    resolution:
      {
        integrity: sha512-9jPkMiIBXvPc2KywkraqsUfbfj+dHDb+JPWtSJa9MLFdrPyazI7q6WX2sUrm7R9eVR7qqv3Pas7EvQFzxKnI6w==,
      }
    engines: { node: ">=14.0.0" }
    hasBin: true

  tailwindcss@4.1.11:
    resolution:
      {
        integrity: sha512-2E9TBm6MDD/xKYe+dvJZAmg3yxIEDNRc0jwlNyDg/4Fil2QcSLjFKGVff0lAf1jjeaArlG/M75Ey/EYr/OJtBA==,
      }

  tapable@2.2.2:
    resolution:
      {
        integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==,
      }
    engines: { node: ">=6" }

  tar-fs@2.1.3:
    resolution:
      {
        integrity: sha512-090nwYJDmlhwFwEW3QQl+vaNnxsO2yVsd45eTKRBzSzu+hlb1w2K9inVq5b0ngXuLVqQ4ApvsUHHnu/zQNkWAg==,
      }

  tar-stream@2.2.0:
    resolution:
      {
        integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==,
      }
    engines: { node: ">=6" }

  tar@7.4.3:
    resolution:
      {
        integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==,
      }
    engines: { node: ">=18" }

  test-exclude@7.0.1:
    resolution:
      {
        integrity: sha512-pFYqmTw68LXVjeWJMST4+borgQP2AyMNbg1BpZh9LbyhUeNkeaPF9gzfPGUAnSMV3qPYdWUwDIjjCLiSDOl7vg==,
      }
    engines: { node: ">=18" }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
      }
    engines: { node: ">=0.8" }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
      }

  tiny-inflate@1.0.3:
    resolution:
      {
        integrity: sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==,
      }

  tinybench@2.9.0:
    resolution:
      {
        integrity: sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==,
      }

  tinyexec@0.3.2:
    resolution:
      {
        integrity: sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==,
      }

  tinyglobby@0.2.14:
    resolution:
      {
        integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==,
      }
    engines: { node: ">=12.0.0" }

  tinypool@1.1.1:
    resolution:
      {
        integrity: sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==,
      }
    engines: { node: ^18.0.0 || >=20.0.0 }

  tinyrainbow@2.0.0:
    resolution:
      {
        integrity: sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==,
      }
    engines: { node: ">=14.0.0" }

  tinyspy@4.0.3:
    resolution:
      {
        integrity: sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==,
      }
    engines: { node: ">=14.0.0" }

  tldts-core@6.1.86:
    resolution:
      {
        integrity: sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA==,
      }

  tldts@6.1.86:
    resolution:
      {
        integrity: sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ==,
      }
    hasBin: true

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
      }
    engines: { node: ">=8.0" }

  tough-cookie@5.1.2:
    resolution:
      {
        integrity: sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==,
      }
    engines: { node: ">=16" }

  tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==,
      }

  tr46@5.1.1:
    resolution:
      {
        integrity: sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw==,
      }
    engines: { node: ">=18" }

  trim-lines@3.0.1:
    resolution:
      {
        integrity: sha512-kRj8B+YHZCc9kQYdWfJB2/oUl9rA99qbowYYBtr4ui4mZyAQ2JpvVBd/6U2YloATfqBhBTSMhTpgBHtU0Mf3Rg==,
      }

  trough@2.2.0:
    resolution:
      {
        integrity: sha512-tmMpK00BjZiUyVyvrBK7knerNgmgvcV/KLVyuma/SC+TQN167GrMRciANTz09+k3zW8L8t60jWO1GpfkZdjTaw==,
      }

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
      }

  tsconfck@3.1.6:
    resolution:
      {
        integrity: sha512-ks6Vjr/jEw0P1gmOVwutM3B7fWxoWBL2KRDb1JfqGVawBmO5UsvmWOQFGHBPl5yxYz4eERr19E6L7NMv+Fej4w==,
      }
    engines: { node: ^18 || >=20 }
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0
    peerDependenciesMeta:
      typescript:
        optional: true

  tslib@1.14.1:
    resolution:
      {
        integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==,
      }

  tslib@2.8.1:
    resolution:
      {
        integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==,
      }

  tsx@4.20.3:
    resolution:
      {
        integrity: sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==,
      }
    engines: { node: ">=18.0.0" }
    hasBin: true

  tunnel-agent@0.6.0:
    resolution:
      {
        integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==,
      }

  tw-animate-css@1.3.4:
    resolution:
      {
        integrity: sha512-dd1Ht6/YQHcNbq0znIT6dG8uhO7Ce+VIIhZUhjsryXsMPJQz3bZg7Q2eNzLwipb25bRZslGb2myio5mScd1TFg==,
      }

  tw-to-css@0.0.12:
    resolution:
      {
        integrity: sha512-rQAsQvOtV1lBkyCw+iypMygNHrShYAItES5r8fMsrhhaj5qrV2LkZyXc8ccEH+u5bFjHjQ9iuxe90I7Kykf6pw==,
      }
    engines: { node: ">=16.0.0" }

  type-fest@4.41.0:
    resolution:
      {
        integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==,
      }
    engines: { node: ">=16" }

  typesafe-path@0.2.2:
    resolution:
      {
        integrity: sha512-OJabfkAg1WLZSqJAJ0Z6Sdt3utnbzr/jh+NAHoyWHJe8CMSy79Gm085094M9nvTPy22KzTVn5Zq5mbapCI/hPA==,
      }

  typescript-auto-import-cache@0.3.6:
    resolution:
      {
        integrity: sha512-RpuHXrknHdVdK7wv/8ug3Fr0WNsNi5l5aB8MYYuXhq2UH5lnEB1htJ1smhtD5VeCsGr2p8mUDtd83LCQDFVgjQ==,
      }

  typescript@5.8.3:
    resolution:
      {
        integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==,
      }
    engines: { node: ">=14.17" }
    hasBin: true

  ufo@1.6.1:
    resolution:
      {
        integrity: sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==,
      }

  ultrahtml@1.6.0:
    resolution:
      {
        integrity: sha512-R9fBn90VTJrqqLDwyMph+HGne8eqY1iPfYhPzZrvKpIfwkWZbcYlfpsb8B9dTvBfpy1/hqAD7Wi8EKfP9e8zdw==,
      }

  uncrypto@0.1.3:
    resolution:
      {
        integrity: sha512-Ql87qFHB3s/De2ClA9e0gsnS6zXG27SkTiSJwjCc9MebbfapQfuPzumMIUMi38ezPZVNFcHI9sUIepeQfw8J8Q==,
      }

  undici-types@6.21.0:
    resolution:
      {
        integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==,
      }

  undici-types@7.8.0:
    resolution:
      {
        integrity: sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==,
      }

  unicode-properties@1.4.1:
    resolution:
      {
        integrity: sha512-CLjCCLQ6UuMxWnbIylkisbRj31qxHPAurvena/0iwSVbQ2G1VY5/HjV0IRabOEbDHlzZlRdCrD4NhB0JtU40Pg==,
      }

  unicode-trie@2.0.0:
    resolution:
      {
        integrity: sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==,
      }

  unified@11.0.5:
    resolution:
      {
        integrity: sha512-xKvGhPWw3k84Qjh8bI3ZeJjqnyadK+GEFtazSfZv/rKeTkTjOJho6mFqh2SM96iIcZokxiOpg78GazTSg8+KHA==,
      }

  unifont@0.5.0:
    resolution:
      {
        integrity: sha512-4DueXMP5Hy4n607sh+vJ+rajoLu778aU3GzqeTCqsD/EaUcvqZT9wPC8kgK6Vjh22ZskrxyRCR71FwNOaYn6jA==,
      }

  unist-util-find-after@5.0.0:
    resolution:
      {
        integrity: sha512-amQa0Ep2m6hE2g72AugUItjbuM8X8cGQnFoHk0pGfrFeT9GZhzN5SW8nRsiGKK7Aif4CrACPENkA6P/Lw6fHGQ==,
      }

  unist-util-is@6.0.0:
    resolution:
      {
        integrity: sha512-2qCTHimwdxLfz+YzdGfkqNlH0tLi9xjTnHddPmJwtIG9MGsdbutfTc4P+haPD7l7Cjxf/WZj+we5qfVPvvxfYw==,
      }

  unist-util-modify-children@4.0.0:
    resolution:
      {
        integrity: sha512-+tdN5fGNddvsQdIzUF3Xx82CU9sMM+fA0dLgR9vOmT0oPT2jH+P1nd5lSqfCfXAw+93NhcXNY2qqvTUtE4cQkw==,
      }

  unist-util-position-from-estree@2.0.0:
    resolution:
      {
        integrity: sha512-KaFVRjoqLyF6YXCbVLNad/eS4+OfPQQn2yOd7zF/h5T/CSL2v8NpN6a5TPvtbXthAGw5nG+PuTtq+DdIZr+cRQ==,
      }

  unist-util-position@5.0.0:
    resolution:
      {
        integrity: sha512-fucsC7HjXvkB5R3kTCO7kUjRdrS0BJt3M/FPxmHMBOm8JQi2BsHAHFsy27E0EolP8rp0NzXsJ+jNPyDWvOJZPA==,
      }

  unist-util-remove-position@5.0.0:
    resolution:
      {
        integrity: sha512-Hp5Kh3wLxv0PHj9m2yZhhLt58KzPtEYKQQ4yxfYFEO7EvHwzyDYnduhHnY1mDxoqr7VUwVuHXk9RXKIiYS1N8Q==,
      }

  unist-util-stringify-position@4.0.0:
    resolution:
      {
        integrity: sha512-0ASV06AAoKCDkS2+xw5RXJywruurpbC4JZSm7nr7MOt1ojAzvyyaO+UxZf18j8FCF6kmzCZKcAgN/yu2gm2XgQ==,
      }

  unist-util-visit-children@3.0.0:
    resolution:
      {
        integrity: sha512-RgmdTfSBOg04sdPcpTSD1jzoNBjt9a80/ZCzp5cI9n1qPzLZWF9YdvWGN2zmTumP1HWhXKdUWexjy/Wy/lJ7tA==,
      }

  unist-util-visit-parents@6.0.1:
    resolution:
      {
        integrity: sha512-L/PqWzfTP9lzzEa6CKs0k2nARxTdZduw3zyh8d2NVBnsyvHjSX4TWse388YrrQKbvI8w20fGjGlhgT96WwKykw==,
      }

  unist-util-visit@5.0.0:
    resolution:
      {
        integrity: sha512-MR04uvD+07cwl/yhVuVWAtw+3GOR/knlL55Nd/wAdblk27GCVt3lqpTivy/tkJcZoNPzTwS1Y+KMojlLDhoTzg==,
      }

  unstorage@1.16.0:
    resolution:
      {
        integrity: sha512-WQ37/H5A7LcRPWfYOrDa1Ys02xAbpPJq6q5GkO88FBXVSQzHd7+BjEwfRqyaSWCv9MbsJy058GWjjPjcJ16GGA==,
      }
    peerDependencies:
      "@azure/app-configuration": ^1.8.0
      "@azure/cosmos": ^4.2.0
      "@azure/data-tables": ^13.3.0
      "@azure/identity": ^4.6.0
      "@azure/keyvault-secrets": ^4.9.0
      "@azure/storage-blob": ^12.26.0
      "@capacitor/preferences": ^6.0.3 || ^7.0.0
      "@deno/kv": ">=0.9.0"
      "@netlify/blobs": ^6.5.0 || ^7.0.0 || ^8.1.0
      "@planetscale/database": ^1.19.0
      "@upstash/redis": ^1.34.3
      "@vercel/blob": ">=0.27.1"
      "@vercel/kv": ^1.0.1
      aws4fetch: ^1.0.20
      db0: ">=0.2.1"
      idb-keyval: ^6.2.1
      ioredis: ^5.4.2
      uploadthing: ^7.4.4
    peerDependenciesMeta:
      "@azure/app-configuration":
        optional: true
      "@azure/cosmos":
        optional: true
      "@azure/data-tables":
        optional: true
      "@azure/identity":
        optional: true
      "@azure/keyvault-secrets":
        optional: true
      "@azure/storage-blob":
        optional: true
      "@capacitor/preferences":
        optional: true
      "@deno/kv":
        optional: true
      "@netlify/blobs":
        optional: true
      "@planetscale/database":
        optional: true
      "@upstash/redis":
        optional: true
      "@vercel/blob":
        optional: true
      "@vercel/kv":
        optional: true
      aws4fetch:
        optional: true
      db0:
        optional: true
      idb-keyval:
        optional: true
      ioredis:
        optional: true
      uploadthing:
        optional: true

  update-browserslist-db@1.1.3:
    resolution:
      {
        integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==,
      }
    hasBin: true
    peerDependencies:
      browserslist: ">= 4.21.0"

  use-callback-ref@1.3.3:
    resolution:
      {
        integrity: sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  use-sidecar@1.1.3:
    resolution:
      {
        integrity: sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      "@types/react": "*"
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      "@types/react":
        optional: true

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
      }

  uuid@11.1.0:
    resolution:
      {
        integrity: sha512-0/A9rDy9P7cJ+8w1c9WD9V//9Wj15Ce2MPz8Ri6032usz+NfePxx5AcN3bN+r6ZL6jEo066/yNYB3tn4pQEx+A==,
      }
    hasBin: true

  vfile-location@5.0.3:
    resolution:
      {
        integrity: sha512-5yXvWDEgqeiYiBe1lbxYF7UMAIm/IcopxMHrMQDq3nvKcjPKIhZklUKL+AE7J7uApI4kwe2snsK+eI6UTj9EHg==,
      }

  vfile-message@4.0.2:
    resolution:
      {
        integrity: sha512-jRDZ1IMLttGj41KcZvlrYAaI3CfqpLpfpf+Mfig13viT6NKvRzWZ+lXz0Y5D60w6uJIBAOGq9mSHf0gktF0duw==,
      }

  vfile@6.0.3:
    resolution:
      {
        integrity: sha512-KzIbH/9tXat2u30jf+smMwFCsno4wHVdNmzFyL+T/L3UGqqk6JKfVqOFOZEpZSHADH1k40ab6NUIXZq422ov3Q==,
      }

  vite-node@3.2.4:
    resolution:
      {
        integrity: sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==,
      }
    engines: { node: ^18.0.0 || ^20.0.0 || >=22.0.0 }
    hasBin: true

  vite@6.3.5:
    resolution:
      {
        integrity: sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==,
      }
    engines: { node: ^18.0.0 || ^20.0.0 || >=22.0.0 }
    hasBin: true
    peerDependencies:
      "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: ">=1.21.0"
      less: "*"
      lightningcss: ^1.21.0
      sass: "*"
      sass-embedded: "*"
      stylus: "*"
      sugarss: "*"
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      "@types/node":
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vitefu@1.0.6:
    resolution:
      {
        integrity: sha512-+Rex1GlappUyNN6UfwbVZne/9cYC4+R2XDk9xkNXBKMw6HQagdX9PgZ8V2v1WUSK1wfBLp7qbI1+XSNIlB1xmA==,
      }
    peerDependencies:
      vite: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    peerDependenciesMeta:
      vite:
        optional: true

  vitest@3.2.4:
    resolution:
      {
        integrity: sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==,
      }
    engines: { node: ^18.0.0 || ^20.0.0 || >=22.0.0 }
    hasBin: true
    peerDependencies:
      "@edge-runtime/vm": "*"
      "@types/debug": ^4.1.12
      "@types/node": ^18.0.0 || ^20.0.0 || >=22.0.0
      "@vitest/browser": 3.2.4
      "@vitest/ui": 3.2.4
      happy-dom: "*"
      jsdom: "*"
    peerDependenciesMeta:
      "@edge-runtime/vm":
        optional: true
      "@types/debug":
        optional: true
      "@types/node":
        optional: true
      "@vitest/browser":
        optional: true
      "@vitest/ui":
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  volar-service-css@0.0.62:
    resolution:
      {
        integrity: sha512-JwNyKsH3F8PuzZYuqPf+2e+4CTU8YoyUHEHVnoXNlrLe7wy9U3biomZ56llN69Ris7TTy/+DEX41yVxQpM4qvg==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  volar-service-emmet@0.0.62:
    resolution:
      {
        integrity: sha512-U4dxWDBWz7Pi4plpbXf4J4Z/ss6kBO3TYrACxWNsE29abu75QzVS0paxDDhI6bhqpbDFXlpsDhZ9aXVFpnfGRQ==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  volar-service-html@0.0.62:
    resolution:
      {
        integrity: sha512-Zw01aJsZRh4GTGUjveyfEzEqpULQUdQH79KNEiKVYHZyuGtdBRYCHlrus1sueSNMxwwkuF5WnOHfvBzafs8yyQ==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  volar-service-prettier@0.0.62:
    resolution:
      {
        integrity: sha512-h2yk1RqRTE+vkYZaI9KYuwpDfOQRrTEMvoHol0yW4GFKc75wWQRrb5n/5abDrzMPrkQbSip8JH2AXbvrRtYh4w==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
      prettier: ^2.2 || ^3.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true
      prettier:
        optional: true

  volar-service-typescript-twoslash-queries@0.0.62:
    resolution:
      {
        integrity: sha512-KxFt4zydyJYYI0kFAcWPTh4u0Ha36TASPZkAnNY784GtgajerUqM80nX/W1d0wVhmcOFfAxkVsf/Ed+tiYU7ng==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  volar-service-typescript@0.0.62:
    resolution:
      {
        integrity: sha512-p7MPi71q7KOsH0eAbZwPBiKPp9B2+qrdHAd6VY5oTo9BUXatsOAdakTm9Yf0DUj6uWBAaOT01BSeVOPwucMV1g==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  volar-service-yaml@0.0.62:
    resolution:
      {
        integrity: sha512-k7gvv7sk3wa+nGll3MaSKyjwQsJjIGCHFjVkl3wjaSP2nouKyn9aokGmqjrl39mi88Oy49giog2GkZH526wjig==,
      }
    peerDependencies:
      "@volar/language-service": ~2.4.0
    peerDependenciesMeta:
      "@volar/language-service":
        optional: true

  vscode-css-languageservice@6.3.6:
    resolution:
      {
        integrity: sha512-fU4h8mT3KlvfRcbF74v/M+Gzbligav6QMx4AD/7CbclWPYOpGb9kgIswfpZVJbIcOEJJACI9iYizkNwdiAqlHw==,
      }

  vscode-html-languageservice@5.5.0:
    resolution:
      {
        integrity: sha512-No6Er2P2L8IsXDnUFlp0bP4f2sdkJv+zJLZYFhtEQIp+2xNfxY8WYkhSxLJ/7bZhuV/aU55lmGSSHBVxSGer3Q==,
      }

  vscode-json-languageservice@4.1.8:
    resolution:
      {
        integrity: sha512-0vSpg6Xd9hfV+eZAaYN63xVVMOTmJ4GgHxXnkLCh+9RsQBkWKIghzLhW2B9ebfG+LQQg8uLtsQ2aUKjTgE+QOg==,
      }
    engines: { npm: ">=7.0.0" }

  vscode-jsonrpc@6.0.0:
    resolution:
      {
        integrity: sha512-wnJA4BnEjOSyFMvjZdpiOwhSq9uDoK8e/kpRJDTaMYzwlkrhG1fwDIZI94CLsLzlCK5cIbMMtFlJlfR57Lavmg==,
      }
    engines: { node: ">=8.0.0 || >=10.0.0" }

  vscode-jsonrpc@8.2.0:
    resolution:
      {
        integrity: sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==,
      }
    engines: { node: ">=14.0.0" }

  vscode-languageserver-protocol@3.16.0:
    resolution:
      {
        integrity: sha512-sdeUoAawceQdgIfTI+sdcwkiK2KU+2cbEYA0agzM2uqaUy2UpnnGHtWTHVEtS0ES4zHU0eMFRGN+oQgDxlD66A==,
      }

  vscode-languageserver-protocol@3.17.5:
    resolution:
      {
        integrity: sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==,
      }

  vscode-languageserver-textdocument@1.0.12:
    resolution:
      {
        integrity: sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==,
      }

  vscode-languageserver-types@3.16.0:
    resolution:
      {
        integrity: sha512-k8luDIWJWyenLc5ToFQQMaSrqCHiLwyKPHKPQZ5zz21vM+vIVUSvsRpcbiECH4WR88K2XZqc4ScRcZ7nk/jbeA==,
      }

  vscode-languageserver-types@3.17.5:
    resolution:
      {
        integrity: sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==,
      }

  vscode-languageserver@7.0.0:
    resolution:
      {
        integrity: sha512-60HTx5ID+fLRcgdHfmz0LDZAXYEV68fzwG0JWwEPBode9NuMYTIxuYXPg4ngO8i8+Ou0lM7y6GzaYWbiDL0drw==,
      }
    hasBin: true

  vscode-languageserver@9.0.1:
    resolution:
      {
        integrity: sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==,
      }
    hasBin: true

  vscode-nls@5.2.0:
    resolution:
      {
        integrity: sha512-RAaHx7B14ZU04EU31pT+rKz2/zSl7xMsfIZuo8pd+KZO6PXtQmpevpq3vxvWNcrGbdmhM/rr5Uw5Mz+NBfhVng==,
      }

  vscode-uri@3.1.0:
    resolution:
      {
        integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==,
      }

  w3c-xmlserializer@5.0.0:
    resolution:
      {
        integrity: sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==,
      }
    engines: { node: ">=18" }

  wasm-feature-detect@1.8.0:
    resolution:
      {
        integrity: sha512-zksaLKM2fVlnB5jQQDqKXXwYHLQUVH9es+5TOOHwGOVJOCeRBCiPjwSg+3tN2AdTCzjgli4jijCH290kXb/zWQ==,
      }

  web-namespaces@2.0.1:
    resolution:
      {
        integrity: sha512-bKr1DkiNa2krS7qxNtdrtHAmzuYGFQLiQ13TsorsdT6ULTkPLKuu5+GsFpDlg6JFjUTwX2DyhMPG2be8uPrqsQ==,
      }

  webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==,
      }

  webidl-conversions@7.0.0:
    resolution:
      {
        integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==,
      }
    engines: { node: ">=12" }

  whatwg-encoding@3.1.1:
    resolution:
      {
        integrity: sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==,
      }
    engines: { node: ">=18" }

  whatwg-mimetype@3.0.0:
    resolution:
      {
        integrity: sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==,
      }
    engines: { node: ">=12" }

  whatwg-mimetype@4.0.0:
    resolution:
      {
        integrity: sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==,
      }
    engines: { node: ">=18" }

  whatwg-url@14.2.0:
    resolution:
      {
        integrity: sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==,
      }
    engines: { node: ">=18" }

  whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==,
      }

  which-module@2.0.1:
    resolution:
      {
        integrity: sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==,
      }

  which-pm-runs@1.1.0:
    resolution:
      {
        integrity: sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==,
      }
    engines: { node: ">=4" }

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
      }
    engines: { node: ">= 8" }
    hasBin: true

  why-is-node-running@2.3.0:
    resolution:
      {
        integrity: sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==,
      }
    engines: { node: ">=8" }
    hasBin: true

  widest-line@5.0.0:
    resolution:
      {
        integrity: sha512-c9bZp7b5YtRj2wOe6dlj32MK+Bx/M/d+9VB2SHM1OtsUHR0aV0tdP6DWh/iMt0kWi1t5g1Iudu6hQRNd1A4PVA==,
      }
    engines: { node: ">=18" }

  wrap-ansi@6.2.0:
    resolution:
      {
        integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==,
      }
    engines: { node: ">=8" }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
      }
    engines: { node: ">=10" }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
      }
    engines: { node: ">=12" }

  wrap-ansi@9.0.0:
    resolution:
      {
        integrity: sha512-G8ura3S+3Z2G+mkgNRq8dqaFZAuxfsxpBB8OCTGRTCtp+l/v9nbFNmCUP1BZMts3G1142MsZfn6eeUKrr4PD1Q==,
      }
    engines: { node: ">=18" }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
      }

  ws@8.18.2:
    resolution:
      {
        integrity: sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==,
      }
    engines: { node: ">=10.0.0" }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ">=5.0.2"
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@5.0.0:
    resolution:
      {
        integrity: sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==,
      }
    engines: { node: ">=18" }

  xmlchars@2.2.0:
    resolution:
      {
        integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==,
      }

  xxhash-wasm@1.1.0:
    resolution:
      {
        integrity: sha512-147y/6YNh+tlp6nd/2pWq38i9h6mz/EuQ6njIrmW8D1BS5nCqs0P6DG+m6zTGnNz5I+uhZ0SHxBs9BsPrwcKDA==,
      }

  y18n@4.0.3:
    resolution:
      {
        integrity: sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ==,
      }

  y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
      }
    engines: { node: ">=10" }

  yallist@3.1.1:
    resolution:
      {
        integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
      }

  yallist@5.0.0:
    resolution:
      {
        integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==,
      }
    engines: { node: ">=18" }

  yaml-language-server@1.15.0:
    resolution:
      {
        integrity: sha512-N47AqBDCMQmh6mBLmI6oqxryHRzi33aPFPsJhYy3VTUGCdLHYjGh4FZzpUjRlphaADBBkDmnkM/++KNIOHi5Rw==,
      }
    hasBin: true

  yaml@2.2.2:
    resolution:
      {
        integrity: sha512-CBKFWExMn46Foo4cldiChEzn7S7SRV+wqiluAb6xmueD/fGyRHIhX8m14vVGgeFWjN540nKCNVj6P21eQjgTuA==,
      }
    engines: { node: ">= 14" }

  yaml@2.8.0:
    resolution:
      {
        integrity: sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==,
      }
    engines: { node: ">= 14.6" }
    hasBin: true

  yargs-parser@18.1.3:
    resolution:
      {
        integrity: sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==,
      }
    engines: { node: ">=6" }

  yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
      }
    engines: { node: ">=12" }

  yargs@15.4.1:
    resolution:
      {
        integrity: sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A==,
      }
    engines: { node: ">=8" }

  yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
      }
    engines: { node: ">=12" }

  yocto-queue@1.2.1:
    resolution:
      {
        integrity: sha512-AyeEbWOu/TAXdxlV9wmGcR0+yh2j3vYPGOECcIj2S7MkrLyC7ne+oye2BKTItt0ii2PHk4cDy+95+LshzbXnGg==,
      }
    engines: { node: ">=12.20" }

  yocto-spinner@0.2.3:
    resolution:
      {
        integrity: sha512-sqBChb33loEnkoXte1bLg45bEBsOP9N1kzQh5JZNKj/0rik4zAPTNSAVPj3uQAdc6slYJ0Ksc403G2XgxsJQFQ==,
      }
    engines: { node: ">=18.19" }

  yoctocolors@2.1.1:
    resolution:
      {
        integrity: sha512-GQHQqAopRhwU8Kt1DDM8NjibDXHC8eoh1erhGAJPEyveY9qqVeXvVikNKrDz69sHowPMorbPUrH/mx8c50eiBQ==,
      }
    engines: { node: ">=18" }

  yoga-wasm-web@0.3.3:
    resolution:
      {
        integrity: sha512-N+d4UJSJbt/R3wqY7Coqs5pcV0aUj2j9IaQ3rNj9bVCLld8tTGKRa2USARjnvZJWVx1NDmQev8EknoczaOQDOA==,
      }

  zod-to-json-schema@3.24.5:
    resolution:
      {
        integrity: sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==,
      }
    peerDependencies:
      zod: ^3.24.1

  zod-to-ts@1.2.0:
    resolution:
      {
        integrity: sha512-x30XE43V+InwGpvTySRNz9kB7qFU8DlyEy7BsSTCHPH1R0QasMmHWZDCzYm6bVXtj/9NNJAZF3jW8rzFvH5OFA==,
      }
    peerDependencies:
      typescript: ^4.9.4 || ^5.0.2
      zod: ^3

  zod@3.25.67:
    resolution:
      {
        integrity: sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw==,
      }

  zwitch@2.0.4:
    resolution:
      {
        integrity: sha512-bXE4cR/kVZhKZX/RjPEflHaKVhUVl85noU3v6b8apfQEc1x4A+zBxjZ4lN8LqGd6WZ3dl98pY4o717VFmoPp+A==,
      }

snapshots:
  "@alloc/quick-lru@5.2.0": {}

  "@ampproject/remapping@2.3.0":
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25

  "@asamuzakjp/css-color@3.2.0":
    dependencies:
      "@csstools/css-calc": 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-color-parser": 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-parser-algorithms": 3.0.5(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-tokenizer": 3.0.4
      lru-cache: 10.4.3

  "@astrojs/check@0.9.4(typescript@5.8.3)":
    dependencies:
      "@astrojs/language-server": 2.15.4(typescript@5.8.3)
      chokidar: 4.0.3
      kleur: 4.1.5
      typescript: 5.8.3
      yargs: 17.7.2
    transitivePeerDependencies:
      - prettier
      - prettier-plugin-astro

  "@astrojs/compiler@2.12.2": {}

  "@astrojs/internal-helpers@0.6.1": {}

  "@astrojs/language-server@2.15.4(typescript@5.8.3)":
    dependencies:
      "@astrojs/compiler": 2.12.2
      "@astrojs/yaml2ts": 0.2.2
      "@jridgewell/sourcemap-codec": 1.5.0
      "@volar/kit": 2.4.14(typescript@5.8.3)
      "@volar/language-core": 2.4.14
      "@volar/language-server": 2.4.14
      "@volar/language-service": 2.4.14
      fast-glob: 3.3.3
      muggle-string: 0.4.1
      volar-service-css: 0.0.62(@volar/language-service@2.4.14)
      volar-service-emmet: 0.0.62(@volar/language-service@2.4.14)
      volar-service-html: 0.0.62(@volar/language-service@2.4.14)
      volar-service-prettier: 0.0.62(@volar/language-service@2.4.14)
      volar-service-typescript: 0.0.62(@volar/language-service@2.4.14)
      volar-service-typescript-twoslash-queries: 0.0.62(@volar/language-service@2.4.14)
      volar-service-yaml: 0.0.62(@volar/language-service@2.4.14)
      vscode-html-languageservice: 5.5.0
      vscode-uri: 3.1.0
    transitivePeerDependencies:
      - typescript

  "@astrojs/markdown-remark@6.3.2":
    dependencies:
      "@astrojs/internal-helpers": 0.6.1
      "@astrojs/prism": 3.3.0
      github-slugger: 2.0.0
      hast-util-from-html: 2.0.3
      hast-util-to-text: 4.0.2
      import-meta-resolve: 4.1.0
      js-yaml: 4.1.0
      mdast-util-definitions: 6.0.0
      rehype-raw: 7.0.0
      rehype-stringify: 10.0.1
      remark-gfm: 4.0.1
      remark-parse: 11.0.0
      remark-rehype: 11.1.2
      remark-smartypants: 3.0.2
      shiki: 3.7.0
      smol-toml: 1.3.4
      unified: 11.0.5
      unist-util-remove-position: 5.0.0
      unist-util-visit: 5.0.0
      unist-util-visit-parents: 6.0.1
      vfile: 6.0.3
    transitivePeerDependencies:
      - supports-color

  "@astrojs/mdx@4.3.0(astro@5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0))":
    dependencies:
      "@astrojs/markdown-remark": 6.3.2
      "@mdx-js/mdx": 3.1.0(acorn@8.15.0)
      acorn: 8.15.0
      astro: 5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0)
      es-module-lexer: 1.7.0
      estree-util-visit: 2.0.0
      hast-util-to-html: 9.0.5
      kleur: 4.1.5
      rehype-raw: 7.0.0
      remark-gfm: 4.0.1
      remark-smartypants: 3.0.2
      source-map: 0.7.4
      unist-util-visit: 5.0.0
      vfile: 6.0.3
    transitivePeerDependencies:
      - supports-color

  "@astrojs/partytown@2.1.4":
    dependencies:
      "@qwik.dev/partytown": 0.11.1
      mrmime: 2.0.1

  "@astrojs/prism@3.3.0":
    dependencies:
      prismjs: 1.30.0

  "@astrojs/react@4.3.0(@types/node@24.0.3)(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(jiti@2.4.2)(lightningcss@1.30.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tsx@4.20.3)(yaml@2.8.0)":
    dependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)
      "@vitejs/plugin-react": 4.5.2(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      ultrahtml: 1.6.0
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - "@types/node"
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  "@astrojs/sitemap@3.4.1":
    dependencies:
      sitemap: 8.0.0
      stream-replace-string: 2.0.0
      zod: 3.25.67

  "@astrojs/telemetry@3.3.0":
    dependencies:
      ci-info: 4.2.0
      debug: 4.4.1
      dlv: 1.1.3
      dset: 3.1.4
      is-docker: 3.0.0
      is-wsl: 3.1.0
      which-pm-runs: 1.1.0
    transitivePeerDependencies:
      - supports-color

  "@astrojs/yaml2ts@0.2.2":
    dependencies:
      yaml: 2.8.0

  "@babel/code-frame@7.27.1":
    dependencies:
      "@babel/helper-validator-identifier": 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  "@babel/compat-data@7.27.5": {}

  "@babel/core@7.27.4":
    dependencies:
      "@ampproject/remapping": 2.3.0
      "@babel/code-frame": 7.27.1
      "@babel/generator": 7.27.5
      "@babel/helper-compilation-targets": 7.27.2
      "@babel/helper-module-transforms": 7.27.3(@babel/core@7.27.4)
      "@babel/helpers": 7.27.6
      "@babel/parser": 7.27.5
      "@babel/template": 7.27.2
      "@babel/traverse": 7.27.4
      "@babel/types": 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  "@babel/generator@7.27.5":
    dependencies:
      "@babel/parser": 7.27.5
      "@babel/types": 7.27.6
      "@jridgewell/gen-mapping": 0.3.8
      "@jridgewell/trace-mapping": 0.3.25
      jsesc: 3.1.0

  "@babel/helper-compilation-targets@7.27.2":
    dependencies:
      "@babel/compat-data": 7.27.5
      "@babel/helper-validator-option": 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  "@babel/helper-module-imports@7.27.1":
    dependencies:
      "@babel/traverse": 7.27.4
      "@babel/types": 7.27.6
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)":
    dependencies:
      "@babel/core": 7.27.4
      "@babel/helper-module-imports": 7.27.1
      "@babel/helper-validator-identifier": 7.27.1
      "@babel/traverse": 7.27.4
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-plugin-utils@7.27.1": {}

  "@babel/helper-string-parser@7.27.1": {}

  "@babel/helper-validator-identifier@7.27.1": {}

  "@babel/helper-validator-option@7.27.1": {}

  "@babel/helpers@7.27.6":
    dependencies:
      "@babel/template": 7.27.2
      "@babel/types": 7.27.6

  "@babel/parser@7.27.5":
    dependencies:
      "@babel/types": 7.27.6

  "@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.4)":
    dependencies:
      "@babel/core": 7.27.4
      "@babel/helper-plugin-utils": 7.27.1

  "@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.4)":
    dependencies:
      "@babel/core": 7.27.4
      "@babel/helper-plugin-utils": 7.27.1

  "@babel/runtime@7.27.6": {}

  "@babel/template@7.27.2":
    dependencies:
      "@babel/code-frame": 7.27.1
      "@babel/parser": 7.27.5
      "@babel/types": 7.27.6

  "@babel/traverse@7.27.4":
    dependencies:
      "@babel/code-frame": 7.27.1
      "@babel/generator": 7.27.5
      "@babel/parser": 7.27.5
      "@babel/template": 7.27.2
      "@babel/types": 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  "@babel/types@7.27.6":
    dependencies:
      "@babel/helper-string-parser": 7.27.1
      "@babel/helper-validator-identifier": 7.27.1

  "@bcoe/v8-coverage@1.0.2": {}

  "@capsizecss/unpack@2.4.0":
    dependencies:
      blob-to-buffer: 1.2.9
      cross-fetch: 3.2.0
      fontkit: 2.0.4
    transitivePeerDependencies:
      - encoding

  "@csstools/color-helpers@5.0.2": {}

  "@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)":
    dependencies:
      "@csstools/css-parser-algorithms": 3.0.5(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-tokenizer": 3.0.4

  "@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)":
    dependencies:
      "@csstools/color-helpers": 5.0.2
      "@csstools/css-calc": 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-parser-algorithms": 3.0.5(@csstools/css-tokenizer@3.0.4)
      "@csstools/css-tokenizer": 3.0.4

  "@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)":
    dependencies:
      "@csstools/css-tokenizer": 3.0.4

  "@csstools/css-tokenizer@3.0.4": {}

  "@emmetio/abbreviation@2.3.3":
    dependencies:
      "@emmetio/scanner": 1.0.4

  "@emmetio/css-abbreviation@2.1.8":
    dependencies:
      "@emmetio/scanner": 1.0.4

  "@emmetio/css-parser@0.4.0":
    dependencies:
      "@emmetio/stream-reader": 2.2.0
      "@emmetio/stream-reader-utils": 0.1.0

  "@emmetio/html-matcher@1.3.0":
    dependencies:
      "@emmetio/scanner": 1.0.4

  "@emmetio/scanner@1.0.4": {}

  "@emmetio/stream-reader-utils@0.1.0": {}

  "@emmetio/stream-reader@2.2.0": {}

  "@emnapi/runtime@1.4.3":
    dependencies:
      tslib: 2.8.1
    optional: true

  "@esbuild/aix-ppc64@0.25.5":
    optional: true

  "@esbuild/android-arm64@0.25.5":
    optional: true

  "@esbuild/android-arm@0.25.5":
    optional: true

  "@esbuild/android-x64@0.25.5":
    optional: true

  "@esbuild/darwin-arm64@0.25.5":
    optional: true

  "@esbuild/darwin-x64@0.25.5":
    optional: true

  "@esbuild/freebsd-arm64@0.25.5":
    optional: true

  "@esbuild/freebsd-x64@0.25.5":
    optional: true

  "@esbuild/linux-arm64@0.25.5":
    optional: true

  "@esbuild/linux-arm@0.25.5":
    optional: true

  "@esbuild/linux-ia32@0.25.5":
    optional: true

  "@esbuild/linux-loong64@0.25.5":
    optional: true

  "@esbuild/linux-mips64el@0.25.5":
    optional: true

  "@esbuild/linux-ppc64@0.25.5":
    optional: true

  "@esbuild/linux-riscv64@0.25.5":
    optional: true

  "@esbuild/linux-s390x@0.25.5":
    optional: true

  "@esbuild/linux-x64@0.25.5":
    optional: true

  "@esbuild/netbsd-arm64@0.25.5":
    optional: true

  "@esbuild/netbsd-x64@0.25.5":
    optional: true

  "@esbuild/openbsd-arm64@0.25.5":
    optional: true

  "@esbuild/openbsd-x64@0.25.5":
    optional: true

  "@esbuild/sunos-x64@0.25.5":
    optional: true

  "@esbuild/win32-arm64@0.25.5":
    optional: true

  "@esbuild/win32-ia32@0.25.5":
    optional: true

  "@esbuild/win32-x64@0.25.5":
    optional: true

  "@floating-ui/core@1.7.1":
    dependencies:
      "@floating-ui/utils": 0.2.9

  "@floating-ui/dom@1.7.1":
    dependencies:
      "@floating-ui/core": 1.7.1
      "@floating-ui/utils": 0.2.9

  "@floating-ui/react-dom@2.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@floating-ui/dom": 1.7.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  "@floating-ui/utils@0.2.9": {}

  "@fontsource/inter@5.2.6": {}

  "@img/sharp-darwin-arm64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-darwin-arm64": 1.0.4
    optional: true

  "@img/sharp-darwin-x64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-darwin-x64": 1.0.4
    optional: true

  "@img/sharp-libvips-darwin-arm64@1.0.4":
    optional: true

  "@img/sharp-libvips-darwin-x64@1.0.4":
    optional: true

  "@img/sharp-libvips-linux-arm64@1.0.4":
    optional: true

  "@img/sharp-libvips-linux-arm@1.0.5":
    optional: true

  "@img/sharp-libvips-linux-s390x@1.0.4":
    optional: true

  "@img/sharp-libvips-linux-x64@1.0.4":
    optional: true

  "@img/sharp-libvips-linuxmusl-arm64@1.0.4":
    optional: true

  "@img/sharp-libvips-linuxmusl-x64@1.0.4":
    optional: true

  "@img/sharp-linux-arm64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linux-arm64": 1.0.4
    optional: true

  "@img/sharp-linux-arm@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linux-arm": 1.0.5
    optional: true

  "@img/sharp-linux-s390x@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linux-s390x": 1.0.4
    optional: true

  "@img/sharp-linux-x64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linux-x64": 1.0.4
    optional: true

  "@img/sharp-linuxmusl-arm64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
    optional: true

  "@img/sharp-linuxmusl-x64@0.33.5":
    optionalDependencies:
      "@img/sharp-libvips-linuxmusl-x64": 1.0.4
    optional: true

  "@img/sharp-wasm32@0.33.5":
    dependencies:
      "@emnapi/runtime": 1.4.3
    optional: true

  "@img/sharp-win32-ia32@0.33.5":
    optional: true

  "@img/sharp-win32-x64@0.33.5":
    optional: true

  "@isaacs/cliui@8.0.2":
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  "@isaacs/fs-minipass@4.0.1":
    dependencies:
      minipass: 7.1.2

  "@istanbuljs/schema@0.1.3": {}

  "@jridgewell/gen-mapping@0.3.8":
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.5.0
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/resolve-uri@3.1.2": {}

  "@jridgewell/set-array@1.2.1": {}

  "@jridgewell/sourcemap-codec@1.5.0": {}

  "@jridgewell/trace-mapping@0.3.25":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.5.0

  "@jsquash/avif@2.1.1":
    dependencies:
      wasm-feature-detect: 1.8.0

  "@jsquash/jpeg@1.6.0": {}

  "@jsquash/png@3.1.1": {}

  "@jsquash/resize@2.1.0": {}

  "@jsquash/webp@1.5.0":
    dependencies:
      wasm-feature-detect: 1.8.0

  "@lucide/astro@0.525.0(astro@5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0))":
    dependencies:
      astro: 5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0)

  "@mdx-js/mdx@3.1.0(acorn@8.15.0)":
    dependencies:
      "@types/estree": 1.0.8
      "@types/estree-jsx": 1.0.5
      "@types/hast": 3.0.4
      "@types/mdx": 2.0.13
      collapse-white-space: 2.1.0
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      estree-util-scope: 1.0.0
      estree-walker: 3.0.3
      hast-util-to-jsx-runtime: 2.3.6
      markdown-extensions: 2.0.0
      recma-build-jsx: 1.0.0
      recma-jsx: 1.0.0(acorn@8.15.0)
      recma-stringify: 1.0.0
      rehype-recma: 1.0.0
      remark-mdx: 3.1.0
      remark-parse: 11.0.0
      remark-rehype: 11.1.2
      source-map: 0.7.4
      unified: 11.0.5
      unist-util-position-from-estree: 2.0.0
      unist-util-stringify-position: 4.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3
    transitivePeerDependencies:
      - acorn
      - supports-color

  "@napi-rs/canvas-android-arm64@0.1.71":
    optional: true

  "@napi-rs/canvas-darwin-arm64@0.1.71":
    optional: true

  "@napi-rs/canvas-darwin-x64@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-arm-gnueabihf@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-arm64-gnu@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-arm64-musl@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-riscv64-gnu@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-x64-gnu@0.1.71":
    optional: true

  "@napi-rs/canvas-linux-x64-musl@0.1.71":
    optional: true

  "@napi-rs/canvas-win32-x64-msvc@0.1.71":
    optional: true

  "@napi-rs/canvas@0.1.71":
    optionalDependencies:
      "@napi-rs/canvas-android-arm64": 0.1.71
      "@napi-rs/canvas-darwin-arm64": 0.1.71
      "@napi-rs/canvas-darwin-x64": 0.1.71
      "@napi-rs/canvas-linux-arm-gnueabihf": 0.1.71
      "@napi-rs/canvas-linux-arm64-gnu": 0.1.71
      "@napi-rs/canvas-linux-arm64-musl": 0.1.71
      "@napi-rs/canvas-linux-riscv64-gnu": 0.1.71
      "@napi-rs/canvas-linux-x64-gnu": 0.1.71
      "@napi-rs/canvas-linux-x64-musl": 0.1.71
      "@napi-rs/canvas-win32-x64-msvc": 0.1.71
    optional: true

  "@nodelib/fs.scandir@2.1.5":
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      run-parallel: 1.2.0

  "@nodelib/fs.stat@2.0.5": {}

  "@nodelib/fs.walk@1.2.8":
    dependencies:
      "@nodelib/fs.scandir": 2.1.5
      fastq: 1.19.1

  "@oslojs/encoding@1.1.0": {}

  "@pdf-lib/standard-fonts@1.0.0":
    dependencies:
      pako: 1.0.11

  "@pdf-lib/upng@1.0.1":
    dependencies:
      pako: 1.0.11

  "@pkgjs/parseargs@0.11.0":
    optional: true

  "@qwik.dev/partytown@0.11.1":
    dependencies:
      dotenv: 16.5.0

  "@radix-ui/number@1.1.1": {}

  "@radix-ui/primitive@1.1.2": {}

  "@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-collapsible": 1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-collection": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-presence": 1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-slot": 1.2.3(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-context@1.1.2(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-dismissable-layer": 1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-focus-guards": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-focus-scope": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-portal": 1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-presence": 1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-slot": 1.2.3(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.1(@types/react@19.1.8)(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-direction@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-escape-keydown": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-hover-card@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-dismissable-layer": 1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-popper": 1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-portal": 1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-presence": 1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-id@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-label@2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-collection": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-dismissable-layer": 1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-presence": 1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-previous": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-visually-hidden": 1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@floating-ui/react-dom": 2.1.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-arrow": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-rect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-size": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/rect": 1.1.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-slot": 1.2.3(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-progress@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-collection": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-select@2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/number": 1.1.1
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-collection": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-dismissable-layer": 1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-focus-guards": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-focus-scope": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-popper": 1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-portal": 1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-slot": 1.2.3(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-previous": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-visually-hidden": 1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.1(@types/react@19.1.8)(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-slider@1.3.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/number": 1.1.1
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-collection": 1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-previous": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-size": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-slot@1.2.3(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-previous": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-size": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-context": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-direction": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-presence": 1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-roving-focus": 1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-toggle@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/primitive": 1.1.2
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-use-controllable-state": 1.2.2(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-use-effect-event": 0.0.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-use-callback-ref": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-previous@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-rect@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/rect": 1.1.1
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-use-size@1.1.1(@types/react@19.1.8)(react@19.1.0)":
    dependencies:
      "@radix-ui/react-use-layout-effect": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      "@types/react": 19.1.8

  "@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8
      "@types/react-dom": 19.1.6(@types/react@19.1.8)

  "@radix-ui/rect@1.1.1": {}

  "@refilelabs/image@0.2.5": {}

  "@resvg/resvg-js-android-arm-eabi@2.6.2":
    optional: true

  "@resvg/resvg-js-android-arm64@2.6.2":
    optional: true

  "@resvg/resvg-js-darwin-arm64@2.6.2":
    optional: true

  "@resvg/resvg-js-darwin-x64@2.6.2":
    optional: true

  "@resvg/resvg-js-linux-arm-gnueabihf@2.6.2":
    optional: true

  "@resvg/resvg-js-linux-arm64-gnu@2.6.2":
    optional: true

  "@resvg/resvg-js-linux-arm64-musl@2.6.2":
    optional: true

  "@resvg/resvg-js-linux-x64-gnu@2.6.2":
    optional: true

  "@resvg/resvg-js-linux-x64-musl@2.6.2":
    optional: true

  "@resvg/resvg-js-win32-arm64-msvc@2.6.2":
    optional: true

  "@resvg/resvg-js-win32-ia32-msvc@2.6.2":
    optional: true

  "@resvg/resvg-js-win32-x64-msvc@2.6.2":
    optional: true

  "@resvg/resvg-js@2.6.2":
    optionalDependencies:
      "@resvg/resvg-js-android-arm-eabi": 2.6.2
      "@resvg/resvg-js-android-arm64": 2.6.2
      "@resvg/resvg-js-darwin-arm64": 2.6.2
      "@resvg/resvg-js-darwin-x64": 2.6.2
      "@resvg/resvg-js-linux-arm-gnueabihf": 2.6.2
      "@resvg/resvg-js-linux-arm64-gnu": 2.6.2
      "@resvg/resvg-js-linux-arm64-musl": 2.6.2
      "@resvg/resvg-js-linux-x64-gnu": 2.6.2
      "@resvg/resvg-js-linux-x64-musl": 2.6.2
      "@resvg/resvg-js-win32-arm64-msvc": 2.6.2
      "@resvg/resvg-js-win32-ia32-msvc": 2.6.2
      "@resvg/resvg-js-win32-x64-msvc": 2.6.2

  "@resvg/resvg-wasm@2.6.2": {}

  "@rolldown/pluginutils@1.0.0-beta.11": {}

  "@rollup/pluginutils@5.2.0(rollup@4.43.0)":
    dependencies:
      "@types/estree": 1.0.8
      estree-walker: 2.0.2
      picomatch: 4.0.2
    optionalDependencies:
      rollup: 4.43.0

  "@rollup/rollup-android-arm-eabi@4.43.0":
    optional: true

  "@rollup/rollup-android-arm64@4.43.0":
    optional: true

  "@rollup/rollup-darwin-arm64@4.43.0":
    optional: true

  "@rollup/rollup-darwin-x64@4.43.0":
    optional: true

  "@rollup/rollup-freebsd-arm64@4.43.0":
    optional: true

  "@rollup/rollup-freebsd-x64@4.43.0":
    optional: true

  "@rollup/rollup-linux-arm-gnueabihf@4.43.0":
    optional: true

  "@rollup/rollup-linux-arm-musleabihf@4.43.0":
    optional: true

  "@rollup/rollup-linux-arm64-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-arm64-musl@4.43.0":
    optional: true

  "@rollup/rollup-linux-loongarch64-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-powerpc64le-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-riscv64-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-riscv64-musl@4.43.0":
    optional: true

  "@rollup/rollup-linux-s390x-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-x64-gnu@4.43.0":
    optional: true

  "@rollup/rollup-linux-x64-musl@4.43.0":
    optional: true

  "@rollup/rollup-win32-arm64-msvc@4.43.0":
    optional: true

  "@rollup/rollup-win32-ia32-msvc@4.43.0":
    optional: true

  "@rollup/rollup-win32-x64-msvc@4.43.0":
    optional: true

  "@shikijs/core@3.7.0":
    dependencies:
      "@shikijs/types": 3.7.0
      "@shikijs/vscode-textmate": 10.0.2
      "@types/hast": 3.0.4
      hast-util-to-html: 9.0.5

  "@shikijs/engine-javascript@3.7.0":
    dependencies:
      "@shikijs/types": 3.7.0
      "@shikijs/vscode-textmate": 10.0.2
      oniguruma-to-es: 4.3.3

  "@shikijs/engine-oniguruma@3.7.0":
    dependencies:
      "@shikijs/types": 3.7.0
      "@shikijs/vscode-textmate": 10.0.2

  "@shikijs/langs@3.7.0":
    dependencies:
      "@shikijs/types": 3.7.0

  "@shikijs/themes@3.7.0":
    dependencies:
      "@shikijs/types": 3.7.0

  "@shikijs/types@3.7.0":
    dependencies:
      "@shikijs/vscode-textmate": 10.0.2
      "@types/hast": 3.0.4

  "@shikijs/vscode-textmate@10.0.2": {}

  "@shuding/opentype.js@1.4.0-beta.0":
    dependencies:
      fflate: 0.7.4
      string.prototype.codepointat: 0.2.1

  "@swc/helpers@0.5.17":
    dependencies:
      tslib: 2.8.1

  "@tailwindcss/node@4.1.11":
    dependencies:
      "@ampproject/remapping": 2.3.0
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.11

  "@tailwindcss/oxide-android-arm64@4.1.11":
    optional: true

  "@tailwindcss/oxide-darwin-arm64@4.1.11":
    optional: true

  "@tailwindcss/oxide-darwin-x64@4.1.11":
    optional: true

  "@tailwindcss/oxide-freebsd-x64@4.1.11":
    optional: true

  "@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11":
    optional: true

  "@tailwindcss/oxide-linux-arm64-gnu@4.1.11":
    optional: true

  "@tailwindcss/oxide-linux-arm64-musl@4.1.11":
    optional: true

  "@tailwindcss/oxide-linux-x64-gnu@4.1.11":
    optional: true

  "@tailwindcss/oxide-linux-x64-musl@4.1.11":
    optional: true

  "@tailwindcss/oxide-wasm32-wasi@4.1.11":
    optional: true

  "@tailwindcss/oxide-win32-arm64-msvc@4.1.11":
    optional: true

  "@tailwindcss/oxide-win32-x64-msvc@4.1.11":
    optional: true

  "@tailwindcss/oxide@4.1.11":
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      "@tailwindcss/oxide-android-arm64": 4.1.11
      "@tailwindcss/oxide-darwin-arm64": 4.1.11
      "@tailwindcss/oxide-darwin-x64": 4.1.11
      "@tailwindcss/oxide-freebsd-x64": 4.1.11
      "@tailwindcss/oxide-linux-arm-gnueabihf": 4.1.11
      "@tailwindcss/oxide-linux-arm64-gnu": 4.1.11
      "@tailwindcss/oxide-linux-arm64-musl": 4.1.11
      "@tailwindcss/oxide-linux-x64-gnu": 4.1.11
      "@tailwindcss/oxide-linux-x64-musl": 4.1.11
      "@tailwindcss/oxide-wasm32-wasi": 4.1.11
      "@tailwindcss/oxide-win32-arm64-msvc": 4.1.11
      "@tailwindcss/oxide-win32-x64-msvc": 4.1.11

  "@tailwindcss/vite@4.1.11(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))":
    dependencies:
      "@tailwindcss/node": 4.1.11
      "@tailwindcss/oxide": 4.1.11
      tailwindcss: 4.1.11
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)

  "@tanstack/react-table@8.21.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@tanstack/table-core": 8.21.3
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  "@tanstack/react-virtual@3.13.12(react-dom@19.1.0(react@19.1.0))(react@19.1.0)":
    dependencies:
      "@tanstack/virtual-core": 3.13.12
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  "@tanstack/table-core@8.21.3": {}

  "@tanstack/virtual-core@3.13.12": {}

  "@types/babel__core@7.20.5":
    dependencies:
      "@babel/parser": 7.27.5
      "@babel/types": 7.27.6
      "@types/babel__generator": 7.27.0
      "@types/babel__template": 7.4.4
      "@types/babel__traverse": 7.20.7

  "@types/babel__generator@7.27.0":
    dependencies:
      "@babel/types": 7.27.6

  "@types/babel__template@7.4.4":
    dependencies:
      "@babel/parser": 7.27.5
      "@babel/types": 7.27.6

  "@types/babel__traverse@7.20.7":
    dependencies:
      "@babel/types": 7.27.6

  "@types/canvas-confetti@1.9.0": {}

  "@types/chai@5.2.2":
    dependencies:
      "@types/deep-eql": 4.0.2

  "@types/debug@4.1.12":
    dependencies:
      "@types/ms": 2.1.0

  "@types/deep-eql@4.0.2": {}

  "@types/estree-jsx@1.0.5":
    dependencies:
      "@types/estree": 1.0.8

  "@types/estree@1.0.7": {}

  "@types/estree@1.0.8": {}

  "@types/file-saver@2.0.7": {}

  "@types/fontkit@2.0.8":
    dependencies:
      "@types/node": 24.0.3

  "@types/hast@3.0.4":
    dependencies:
      "@types/unist": 3.0.3

  "@types/js-yaml@4.0.9": {}

  "@types/jsdom@21.1.7":
    dependencies:
      "@types/node": 24.0.3
      "@types/tough-cookie": 4.0.5
      parse5: 7.3.0

  "@types/mdast@4.0.4":
    dependencies:
      "@types/unist": 3.0.3

  "@types/mdx@2.0.13": {}

  "@types/ms@2.1.0": {}

  "@types/nlcst@2.0.3":
    dependencies:
      "@types/unist": 3.0.3

  "@types/node@17.0.45": {}

  "@types/node@20.19.1":
    dependencies:
      undici-types: 6.21.0

  "@types/node@24.0.3":
    dependencies:
      undici-types: 7.8.0

  "@types/react-dom@19.1.6(@types/react@19.1.8)":
    dependencies:
      "@types/react": 19.1.8

  "@types/react@19.1.8":
    dependencies:
      csstype: 3.1.3

  "@types/sax@1.2.7":
    dependencies:
      "@types/node": 24.0.3

  "@types/tough-cookie@4.0.5": {}

  "@types/unist@2.0.11": {}

  "@types/unist@3.0.3": {}

  "@types/whatwg-mimetype@3.0.2": {}

  "@ungap/structured-clone@1.3.0": {}

  "@vitejs/plugin-react@4.5.2(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))":
    dependencies:
      "@babel/core": 7.27.4
      "@babel/plugin-transform-react-jsx-self": 7.27.1(@babel/core@7.27.4)
      "@babel/plugin-transform-react-jsx-source": 7.27.1(@babel/core@7.27.4)
      "@rolldown/pluginutils": 1.0.0-beta.11
      "@types/babel__core": 7.20.5
      react-refresh: 0.17.0
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  "@vitest/coverage-v8@3.2.4(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.0.3)(happy-dom@18.0.1)(jiti@2.4.2)(jsdom@26.1.0(canvas@3.1.2))(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))":
    dependencies:
      "@ampproject/remapping": 2.3.0
      "@bcoe/v8-coverage": 1.0.2
      ast-v8-to-istanbul: 0.3.3
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 5.0.6
      istanbul-reports: 3.1.7
      magic-string: 0.30.17
      magicast: 0.3.5
      std-env: 3.9.0
      test-exclude: 7.0.1
      tinyrainbow: 2.0.0
      vitest: 3.2.4(@types/debug@4.1.12)(@types/node@24.0.3)(happy-dom@18.0.1)(jiti@2.4.2)(jsdom@26.1.0(canvas@3.1.2))(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  "@vitest/expect@3.2.4":
    dependencies:
      "@types/chai": 5.2.2
      "@vitest/spy": 3.2.4
      "@vitest/utils": 3.2.4
      chai: 5.2.0
      tinyrainbow: 2.0.0

  "@vitest/mocker@3.2.4(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))":
    dependencies:
      "@vitest/spy": 3.2.4
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)

  "@vitest/pretty-format@3.2.4":
    dependencies:
      tinyrainbow: 2.0.0

  "@vitest/runner@3.2.4":
    dependencies:
      "@vitest/utils": 3.2.4
      pathe: 2.0.3
      strip-literal: 3.0.0

  "@vitest/snapshot@3.2.4":
    dependencies:
      "@vitest/pretty-format": 3.2.4
      magic-string: 0.30.17
      pathe: 2.0.3

  "@vitest/spy@3.2.4":
    dependencies:
      tinyspy: 4.0.3

  "@vitest/utils@3.2.4":
    dependencies:
      "@vitest/pretty-format": 3.2.4
      loupe: 3.1.4
      tinyrainbow: 2.0.0

  "@volar/kit@2.4.14(typescript@5.8.3)":
    dependencies:
      "@volar/language-service": 2.4.14
      "@volar/typescript": 2.4.14
      typesafe-path: 0.2.2
      typescript: 5.8.3
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.1.0

  "@volar/language-core@2.4.14":
    dependencies:
      "@volar/source-map": 2.4.14

  "@volar/language-server@2.4.14":
    dependencies:
      "@volar/language-core": 2.4.14
      "@volar/language-service": 2.4.14
      "@volar/typescript": 2.4.14
      path-browserify: 1.0.1
      request-light: 0.7.0
      vscode-languageserver: 9.0.1
      vscode-languageserver-protocol: 3.17.5
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.1.0

  "@volar/language-service@2.4.14":
    dependencies:
      "@volar/language-core": 2.4.14
      vscode-languageserver-protocol: 3.17.5
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.1.0

  "@volar/source-map@2.4.14": {}

  "@volar/typescript@2.4.14":
    dependencies:
      "@volar/language-core": 2.4.14
      path-browserify: 1.0.1
      vscode-uri: 3.1.0

  "@vscode/emmet-helper@2.11.0":
    dependencies:
      emmet: 2.4.11
      jsonc-parser: 2.3.1
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-uri: 3.1.0

  "@vscode/l10n@0.0.18": {}

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  agent-base@7.1.3: {}

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-align@3.0.1:
    dependencies:
      string-width: 4.2.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  aria-query@5.3.2: {}

  array-iterate@2.0.1: {}

  assertion-error@2.0.1: {}

  ast-v8-to-istanbul@0.3.3:
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      estree-walker: 3.0.3
      js-tokens: 9.0.1

  astring@1.9.0: {}

  astro-opengraph-images@1.13.1(canvas@3.1.2)(tw-to-css@0.0.12):
    dependencies:
      "@resvg/resvg-js": 2.6.2
      jsdom: 26.1.0(canvas@3.1.2)
      react: 19.1.0
      satori: 0.15.2
      tw-to-css: 0.0.12
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  astro@5.10.1(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(rollup@4.43.0)(tsx@4.20.3)(typescript@5.8.3)(yaml@2.8.0):
    dependencies:
      "@astrojs/compiler": 2.12.2
      "@astrojs/internal-helpers": 0.6.1
      "@astrojs/markdown-remark": 6.3.2
      "@astrojs/telemetry": 3.3.0
      "@capsizecss/unpack": 2.4.0
      "@oslojs/encoding": 1.1.0
      "@rollup/pluginutils": 5.2.0(rollup@4.43.0)
      acorn: 8.15.0
      aria-query: 5.3.2
      axobject-query: 4.1.0
      boxen: 8.0.1
      ci-info: 4.2.0
      clsx: 2.1.1
      common-ancestor-path: 1.0.1
      cookie: 1.0.2
      cssesc: 3.0.0
      debug: 4.4.1
      deterministic-object-hash: 2.0.2
      devalue: 5.1.1
      diff: 5.2.0
      dlv: 1.1.3
      dset: 3.1.4
      es-module-lexer: 1.7.0
      esbuild: 0.25.5
      estree-walker: 3.0.3
      flattie: 1.1.1
      fontace: 0.3.0
      github-slugger: 2.0.0
      html-escaper: 3.0.3
      http-cache-semantics: 4.2.0
      import-meta-resolve: 4.1.0
      js-yaml: 4.1.0
      kleur: 4.1.5
      magic-string: 0.30.17
      magicast: 0.3.5
      mrmime: 2.0.1
      neotraverse: 0.6.18
      p-limit: 6.2.0
      p-queue: 8.1.0
      package-manager-detector: 1.3.0
      picomatch: 4.0.2
      prompts: 2.4.2
      rehype: 13.0.2
      semver: 7.7.2
      shiki: 3.7.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.14
      tsconfck: 3.1.6(typescript@5.8.3)
      ultrahtml: 1.6.0
      unifont: 0.5.0
      unist-util-visit: 5.0.0
      unstorage: 1.16.0
      vfile: 6.0.3
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
      vitefu: 1.0.6(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      xxhash-wasm: 1.1.0
      yargs-parser: 21.1.1
      yocto-spinner: 0.2.3
      zod: 3.25.67
      zod-to-json-schema: 3.24.5(zod@3.25.67)
      zod-to-ts: 1.2.0(typescript@5.8.3)(zod@3.25.67)
    optionalDependencies:
      sharp: 0.33.5
    transitivePeerDependencies:
      - "@azure/app-configuration"
      - "@azure/cosmos"
      - "@azure/data-tables"
      - "@azure/identity"
      - "@azure/keyvault-secrets"
      - "@azure/storage-blob"
      - "@capacitor/preferences"
      - "@deno/kv"
      - "@netlify/blobs"
      - "@planetscale/database"
      - "@types/node"
      - "@upstash/redis"
      - "@vercel/blob"
      - "@vercel/kv"
      - aws4fetch
      - db0
      - encoding
      - idb-keyval
      - ioredis
      - jiti
      - less
      - lightningcss
      - rollup
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - typescript
      - uploadthing
      - yaml

  axobject-query@4.1.0: {}

  bail@2.0.2: {}

  balanced-match@1.0.2: {}

  base-64@1.0.0: {}

  base64-js@0.0.8: {}

  base64-js@1.5.1: {}

  binary-extensions@2.3.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  blob-to-buffer@1.2.9: {}

  boxen@8.0.1:
    dependencies:
      ansi-align: 3.0.1
      camelcase: 8.0.0
      chalk: 5.4.1
      cli-boxes: 3.0.0
      string-width: 7.2.0
      type-fest: 4.41.0
      widest-line: 5.0.0
      wrap-ansi: 9.0.0

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  brotli@1.3.3:
    dependencies:
      base64-js: 1.5.1

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001723
      electron-to-chromium: 1.5.169
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  cac@6.7.14: {}

  camelcase-css@2.0.1: {}

  camelcase@5.3.1: {}

  camelcase@8.0.0: {}

  camelize@1.0.1: {}

  caniuse-lite@1.0.30001723: {}

  canvas-confetti@1.9.3: {}

  canvas@3.1.2:
    dependencies:
      node-addon-api: 7.1.1
      prebuild-install: 7.1.3

  ccount@2.0.1: {}

  chai@5.2.0:
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.4
      pathval: 2.0.0

  chalk@5.4.1: {}

  character-entities-html4@2.1.0: {}

  character-entities-legacy@3.0.0: {}

  character-entities@2.0.2: {}

  character-reference-invalid@2.0.1: {}

  check-error@2.1.1: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chownr@1.1.4: {}

  chownr@3.0.0: {}

  ci-info@4.2.0: {}

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  cli-boxes@3.0.0: {}

  cliui@6.0.0:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 6.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone@2.1.2: {}

  clsx@2.1.1: {}

  cmdk@1.1.1(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      "@radix-ui/react-compose-refs": 1.1.2(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-dialog": 1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      "@radix-ui/react-id": 1.1.1(@types/react@19.1.8)(react@19.1.0)
      "@radix-ui/react-primitive": 2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    transitivePeerDependencies:
      - "@types/react"
      - "@types/react-dom"

  collapse-white-space@2.1.0: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    optional: true

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1
    optional: true

  comlink@4.4.2: {}

  comma-separated-tokens@2.0.3: {}

  commander@4.1.1: {}

  common-ancestor-path@1.0.1: {}

  convert-source-map@2.0.0: {}

  cookie-es@1.2.2: {}

  cookie@1.0.2: {}

  core-util-is@1.0.3: {}

  cross-fetch@3.2.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crossws@0.3.5:
    dependencies:
      uncrypto: 0.1.3

  css-background-parser@0.1.0: {}

  css-box-shadow@1.0.0-3: {}

  css-color-keywords@1.0.0: {}

  css-gradient-parser@0.0.16: {}

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  css-tree@3.1.0:
    dependencies:
      mdn-data: 2.12.2
      source-map-js: 1.2.1

  cssesc@3.0.0: {}

  cssstyle@4.4.0:
    dependencies:
      "@asamuzakjp/css-color": 3.2.0
      rrweb-cssom: 0.8.0

  csstype@3.1.3: {}

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decamelize@1.2.0: {}

  decimal.js@10.5.0: {}

  decode-named-character-reference@1.2.0:
    dependencies:
      character-entities: 2.0.2

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  deep-eql@5.0.2: {}

  deep-extend@0.6.0: {}

  defu@6.1.4: {}

  dequal@2.0.3: {}

  destr@2.0.5: {}

  detect-libc@2.0.4: {}

  detect-node-es@1.1.0: {}

  deterministic-object-hash@2.0.2:
    dependencies:
      base-64: 1.0.0

  devalue@5.1.1: {}

  devlop@1.1.0:
    dependencies:
      dequal: 2.0.3

  dfa@1.2.0: {}

  didyoumean@1.2.2: {}

  diff@5.2.0: {}

  dijkstrajs@1.0.3: {}

  dlv@1.1.3: {}

  dotenv@16.5.0: {}

  dset@3.1.4: {}

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.5.169: {}

  embla-carousel-react@8.6.0(react@19.1.0):
    dependencies:
      embla-carousel: 8.6.0
      embla-carousel-reactive-utils: 8.6.0(embla-carousel@8.6.0)
      react: 19.1.0

  embla-carousel-reactive-utils@8.6.0(embla-carousel@8.6.0):
    dependencies:
      embla-carousel: 8.6.0

  embla-carousel@8.6.0: {}

  emmet@2.4.11:
    dependencies:
      "@emmetio/abbreviation": 2.3.3
      "@emmetio/css-abbreviation": 2.1.8

  emoji-regex-xs@2.0.1: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  end-of-stream@1.4.5:
    dependencies:
      once: 1.4.0

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@6.0.1: {}

  es-module-lexer@1.7.0: {}

  esast-util-from-estree@2.0.0:
    dependencies:
      "@types/estree-jsx": 1.0.5
      devlop: 1.1.0
      estree-util-visit: 2.0.0
      unist-util-position-from-estree: 2.0.0

  esast-util-from-js@2.0.1:
    dependencies:
      "@types/estree-jsx": 1.0.5
      acorn: 8.15.0
      esast-util-from-estree: 2.0.0
      vfile-message: 4.0.2

  esbuild@0.25.5:
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.25.5
      "@esbuild/android-arm": 0.25.5
      "@esbuild/android-arm64": 0.25.5
      "@esbuild/android-x64": 0.25.5
      "@esbuild/darwin-arm64": 0.25.5
      "@esbuild/darwin-x64": 0.25.5
      "@esbuild/freebsd-arm64": 0.25.5
      "@esbuild/freebsd-x64": 0.25.5
      "@esbuild/linux-arm": 0.25.5
      "@esbuild/linux-arm64": 0.25.5
      "@esbuild/linux-ia32": 0.25.5
      "@esbuild/linux-loong64": 0.25.5
      "@esbuild/linux-mips64el": 0.25.5
      "@esbuild/linux-ppc64": 0.25.5
      "@esbuild/linux-riscv64": 0.25.5
      "@esbuild/linux-s390x": 0.25.5
      "@esbuild/linux-x64": 0.25.5
      "@esbuild/netbsd-arm64": 0.25.5
      "@esbuild/netbsd-x64": 0.25.5
      "@esbuild/openbsd-arm64": 0.25.5
      "@esbuild/openbsd-x64": 0.25.5
      "@esbuild/sunos-x64": 0.25.5
      "@esbuild/win32-arm64": 0.25.5
      "@esbuild/win32-ia32": 0.25.5
      "@esbuild/win32-x64": 0.25.5

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@5.0.0: {}

  estree-util-attach-comments@3.0.0:
    dependencies:
      "@types/estree": 1.0.8

  estree-util-build-jsx@3.0.1:
    dependencies:
      "@types/estree-jsx": 1.0.5
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      estree-walker: 3.0.3

  estree-util-is-identifier-name@3.0.0: {}

  estree-util-scope@1.0.0:
    dependencies:
      "@types/estree": 1.0.8
      devlop: 1.1.0

  estree-util-to-js@2.0.0:
    dependencies:
      "@types/estree-jsx": 1.0.5
      astring: 1.9.0
      source-map: 0.7.4

  estree-util-visit@2.0.0:
    dependencies:
      "@types/estree-jsx": 1.0.5
      "@types/unist": 3.0.3

  estree-walker@2.0.2: {}

  estree-walker@3.0.3:
    dependencies:
      "@types/estree": 1.0.8

  eventemitter3@5.0.1: {}

  expand-template@2.0.3: {}

  expect-type@1.2.1: {}

  extend@3.0.2: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      "@nodelib/fs.walk": 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.6(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  fflate@0.7.4: {}

  file-saver@2.0.5: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  flattie@1.1.1: {}

  fontace@0.3.0:
    dependencies:
      "@types/fontkit": 2.0.8
      fontkit: 2.0.4

  fontkit@2.0.4:
    dependencies:
      "@swc/helpers": 0.5.17
      brotli: 1.3.3
      clone: 2.1.2
      dfa: 1.2.0
      fast-deep-equal: 3.1.3
      restructure: 3.0.2
      tiny-inflate: 1.0.3
      unicode-properties: 1.4.1
      unicode-trie: 2.0.0

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  framer-motion@12.20.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      motion-dom: 12.20.1
      motion-utils: 12.19.0
      tslib: 2.8.1
    optionalDependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  fs-constants@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-east-asian-width@1.3.0: {}

  get-nonce@1.0.1: {}

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  github-from-package@0.0.0: {}

  github-slugger@2.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  globals@11.12.0: {}

  graceful-fs@4.2.11: {}

  h3@1.15.3:
    dependencies:
      cookie-es: 1.2.2
      crossws: 0.3.5
      defu: 6.1.4
      destr: 2.0.5
      iron-webcrypto: 1.2.1
      node-mock-http: 1.0.0
      radix3: 1.1.2
      ufo: 1.6.1
      uncrypto: 0.1.3

  happy-dom@18.0.1:
    dependencies:
      "@types/node": 20.19.1
      "@types/whatwg-mimetype": 3.0.2
      whatwg-mimetype: 3.0.0

  has-flag@4.0.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hast-util-from-html@2.0.3:
    dependencies:
      "@types/hast": 3.0.4
      devlop: 1.1.0
      hast-util-from-parse5: 8.0.3
      parse5: 7.3.0
      vfile: 6.0.3
      vfile-message: 4.0.2

  hast-util-from-parse5@8.0.3:
    dependencies:
      "@types/hast": 3.0.4
      "@types/unist": 3.0.3
      devlop: 1.1.0
      hastscript: 9.0.1
      property-information: 7.1.0
      vfile: 6.0.3
      vfile-location: 5.0.3
      web-namespaces: 2.0.1

  hast-util-is-element@3.0.0:
    dependencies:
      "@types/hast": 3.0.4

  hast-util-parse-selector@4.0.0:
    dependencies:
      "@types/hast": 3.0.4

  hast-util-raw@9.1.0:
    dependencies:
      "@types/hast": 3.0.4
      "@types/unist": 3.0.3
      "@ungap/structured-clone": 1.3.0
      hast-util-from-parse5: 8.0.3
      hast-util-to-parse5: 8.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      parse5: 7.3.0
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3
      web-namespaces: 2.0.1
      zwitch: 2.0.4

  hast-util-to-estree@3.1.3:
    dependencies:
      "@types/estree": 1.0.8
      "@types/estree-jsx": 1.0.5
      "@types/hast": 3.0.4
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      estree-util-attach-comments: 3.0.0
      estree-util-is-identifier-name: 3.0.0
      hast-util-whitespace: 3.0.0
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.2.0
      mdast-util-mdxjs-esm: 2.0.1
      property-information: 7.1.0
      space-separated-tokens: 2.0.2
      style-to-js: 1.1.17
      unist-util-position: 5.0.0
      zwitch: 2.0.4
    transitivePeerDependencies:
      - supports-color

  hast-util-to-html@9.0.5:
    dependencies:
      "@types/hast": 3.0.4
      "@types/unist": 3.0.3
      ccount: 2.0.1
      comma-separated-tokens: 2.0.3
      hast-util-whitespace: 3.0.0
      html-void-elements: 3.0.0
      mdast-util-to-hast: 13.2.0
      property-information: 7.1.0
      space-separated-tokens: 2.0.2
      stringify-entities: 4.0.4
      zwitch: 2.0.4

  hast-util-to-jsx-runtime@2.3.6:
    dependencies:
      "@types/estree": 1.0.8
      "@types/hast": 3.0.4
      "@types/unist": 3.0.3
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      hast-util-whitespace: 3.0.0
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.2.0
      mdast-util-mdxjs-esm: 2.0.1
      property-information: 7.1.0
      space-separated-tokens: 2.0.2
      style-to-js: 1.1.17
      unist-util-position: 5.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  hast-util-to-parse5@8.0.0:
    dependencies:
      "@types/hast": 3.0.4
      comma-separated-tokens: 2.0.3
      devlop: 1.1.0
      property-information: 6.5.0
      space-separated-tokens: 2.0.2
      web-namespaces: 2.0.1
      zwitch: 2.0.4

  hast-util-to-text@4.0.2:
    dependencies:
      "@types/hast": 3.0.4
      "@types/unist": 3.0.3
      hast-util-is-element: 3.0.0
      unist-util-find-after: 5.0.0

  hast-util-whitespace@3.0.0:
    dependencies:
      "@types/hast": 3.0.4

  hastscript@9.0.1:
    dependencies:
      "@types/hast": 3.0.4
      comma-separated-tokens: 2.0.3
      hast-util-parse-selector: 4.0.0
      property-information: 7.1.0
      space-separated-tokens: 2.0.2

  hex-rgb@4.3.0: {}

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  html-escaper@2.0.2: {}

  html-escaper@3.0.3: {}

  html-void-elements@3.0.0: {}

  http-cache-semantics@4.2.0: {}

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  immediate@3.0.6: {}

  import-meta-resolve@4.1.0: {}

  inherits@2.0.4: {}

  ini@1.3.8: {}

  inline-style-parser@0.2.4: {}

  iron-webcrypto@1.2.1: {}

  is-alphabetical@2.0.1: {}

  is-alphanumerical@2.0.1:
    dependencies:
      is-alphabetical: 2.0.1
      is-decimal: 2.0.1

  is-arrayish@0.3.2:
    optional: true

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-decimal@2.0.1: {}

  is-docker@3.0.0: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-hexadecimal@2.0.1: {}

  is-inside-container@1.0.0:
    dependencies:
      is-docker: 3.0.0

  is-number@7.0.0: {}

  is-plain-obj@4.1.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-wsl@3.1.0:
    dependencies:
      is-inside-container: 1.0.0

  isarray@1.0.0: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@5.0.6:
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      debug: 4.4.1
      istanbul-lib-coverage: 3.2.2
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.3:
    dependencies:
      "@isaacs/cliui": 8.0.2
    optionalDependencies:
      "@pkgjs/parseargs": 0.11.0

  jiti@1.21.7: {}

  jiti@2.4.2: {}

  js-tokens@4.0.0: {}

  js-tokens@9.0.1: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@26.1.0(canvas@3.1.2):
    dependencies:
      cssstyle: 4.4.0
      data-urls: 5.0.0
      decimal.js: 10.5.0
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 7.3.0
      rrweb-cssom: 0.8.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 5.1.2
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0
      ws: 8.18.2
      xml-name-validator: 5.0.0
    optionalDependencies:
      canvas: 3.1.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.1.0: {}

  json-schema-traverse@1.0.0: {}

  json5@2.2.3: {}

  jsonc-parser@2.3.1: {}

  jsonc-parser@3.3.1: {}

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  kleur@3.0.3: {}

  kleur@4.1.5: {}

  libheif-js@1.19.8: {}

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lilconfig@2.1.0: {}

  lilconfig@3.1.3: {}

  linebreak@1.1.0:
    dependencies:
      base64-js: 0.0.8
      unicode-trie: 2.0.0

  lines-and-columns@1.2.4: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash@4.17.21: {}

  longest-streak@3.1.0: {}

  loupe@3.1.4: {}

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lucide-react@0.525.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  magic-string@0.30.17:
    dependencies:
      "@jridgewell/sourcemap-codec": 1.5.0

  magicast@0.3.5:
    dependencies:
      "@babel/parser": 7.27.5
      "@babel/types": 7.27.6
      source-map-js: 1.2.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.2

  markdown-extensions@2.0.0: {}

  markdown-table@3.0.4: {}

  mdast-util-definitions@6.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      "@types/unist": 3.0.3
      unist-util-visit: 5.0.0

  mdast-util-find-and-replace@3.0.2:
    dependencies:
      "@types/mdast": 4.0.4
      escape-string-regexp: 5.0.0
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  mdast-util-from-markdown@2.0.2:
    dependencies:
      "@types/mdast": 4.0.4
      "@types/unist": 3.0.3
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      mdast-util-to-string: 4.0.0
      micromark: 4.0.2
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-decode-string: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-stringify-position: 4.0.0
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-autolink-literal@2.0.1:
    dependencies:
      "@types/mdast": 4.0.4
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-find-and-replace: 3.0.2
      micromark-util-character: 2.1.1

  mdast-util-gfm-footnote@2.1.0:
    dependencies:
      "@types/mdast": 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      micromark-util-normalize-identifier: 2.0.1
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-strikethrough@2.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-table@2.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      devlop: 1.1.0
      markdown-table: 3.0.4
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm-task-list-item@2.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-gfm@3.1.0:
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-gfm-autolink-literal: 2.0.1
      mdast-util-gfm-footnote: 2.1.0
      mdast-util-gfm-strikethrough: 2.0.0
      mdast-util-gfm-table: 2.0.0
      mdast-util-gfm-task-list-item: 2.0.0
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-expression@2.0.1:
    dependencies:
      "@types/estree-jsx": 1.0.5
      "@types/hast": 3.0.4
      "@types/mdast": 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx-jsx@3.2.0:
    dependencies:
      "@types/estree-jsx": 1.0.5
      "@types/hast": 3.0.4
      "@types/mdast": 4.0.4
      "@types/unist": 3.0.3
      ccount: 2.0.1
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
      parse-entities: 4.0.2
      stringify-entities: 4.0.4
      unist-util-stringify-position: 4.0.0
      vfile-message: 4.0.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdx@3.0.0:
    dependencies:
      mdast-util-from-markdown: 2.0.2
      mdast-util-mdx-expression: 2.0.1
      mdast-util-mdx-jsx: 3.2.0
      mdast-util-mdxjs-esm: 2.0.1
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-mdxjs-esm@2.0.1:
    dependencies:
      "@types/estree-jsx": 1.0.5
      "@types/hast": 3.0.4
      "@types/mdast": 4.0.4
      devlop: 1.1.0
      mdast-util-from-markdown: 2.0.2
      mdast-util-to-markdown: 2.1.2
    transitivePeerDependencies:
      - supports-color

  mdast-util-phrasing@4.1.0:
    dependencies:
      "@types/mdast": 4.0.4
      unist-util-is: 6.0.0

  mdast-util-to-hast@13.2.0:
    dependencies:
      "@types/hast": 3.0.4
      "@types/mdast": 4.0.4
      "@ungap/structured-clone": 1.3.0
      devlop: 1.1.0
      micromark-util-sanitize-uri: 2.0.1
      trim-lines: 3.0.1
      unist-util-position: 5.0.0
      unist-util-visit: 5.0.0
      vfile: 6.0.3

  mdast-util-to-markdown@2.1.2:
    dependencies:
      "@types/mdast": 4.0.4
      "@types/unist": 3.0.3
      longest-streak: 3.1.0
      mdast-util-phrasing: 4.1.0
      mdast-util-to-string: 4.0.0
      micromark-util-classify-character: 2.0.1
      micromark-util-decode-string: 2.0.1
      unist-util-visit: 5.0.0
      zwitch: 2.0.4

  mdast-util-to-string@4.0.0:
    dependencies:
      "@types/mdast": 4.0.4

  mdn-data@2.12.2: {}

  memoize-one@5.2.1: {}

  merge2@1.4.1: {}

  micromark-core-commonmark@2.0.3:
    dependencies:
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-factory-destination: 2.0.1
      micromark-factory-label: 2.0.1
      micromark-factory-space: 2.0.1
      micromark-factory-title: 2.0.1
      micromark-factory-whitespace: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-html-tag-name: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-autolink-literal@2.1.0:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-footnote@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-strikethrough@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-classify-character: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-table@2.1.1:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm-tagfilter@2.0.0:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-extension-gfm-task-list-item@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-gfm@3.0.0:
    dependencies:
      micromark-extension-gfm-autolink-literal: 2.1.0
      micromark-extension-gfm-footnote: 2.1.0
      micromark-extension-gfm-strikethrough: 2.1.0
      micromark-extension-gfm-table: 2.1.1
      micromark-extension-gfm-tagfilter: 2.0.0
      micromark-extension-gfm-task-list-item: 2.1.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-mdx-expression@3.0.1:
    dependencies:
      "@types/estree": 1.0.8
      devlop: 1.1.0
      micromark-factory-mdx-expression: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.3
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-extension-mdx-jsx@3.0.2:
    dependencies:
      "@types/estree": 1.0.8
      devlop: 1.1.0
      estree-util-is-identifier-name: 3.0.0
      micromark-factory-mdx-expression: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.3
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      vfile-message: 4.0.2

  micromark-extension-mdx-md@2.0.0:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-extension-mdxjs-esm@3.0.0:
    dependencies:
      "@types/estree": 1.0.8
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.3
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-position-from-estree: 2.0.0
      vfile-message: 4.0.2

  micromark-extension-mdxjs@3.0.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      micromark-extension-mdx-expression: 3.0.1
      micromark-extension-mdx-jsx: 3.0.2
      micromark-extension-mdx-md: 2.0.0
      micromark-extension-mdxjs-esm: 3.0.0
      micromark-util-combine-extensions: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-destination@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-label@2.0.1:
    dependencies:
      devlop: 1.1.0
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-mdx-expression@2.0.3:
    dependencies:
      "@types/estree": 1.0.8
      devlop: 1.1.0
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-events-to-acorn: 2.0.3
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      unist-util-position-from-estree: 2.0.0
      vfile-message: 4.0.2

  micromark-factory-space@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-types: 2.0.2

  micromark-factory-title@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-factory-whitespace@2.0.1:
    dependencies:
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-character@2.1.1:
    dependencies:
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-chunked@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-classify-character@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-combine-extensions@2.0.1:
    dependencies:
      micromark-util-chunked: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-decode-numeric-character-reference@2.0.2:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-decode-string@2.0.1:
    dependencies:
      decode-named-character-reference: 1.2.0
      micromark-util-character: 2.1.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-symbol: 2.0.1

  micromark-util-encode@2.0.1: {}

  micromark-util-events-to-acorn@2.0.3:
    dependencies:
      "@types/estree": 1.0.8
      "@types/unist": 3.0.3
      devlop: 1.1.0
      estree-util-visit: 2.0.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
      vfile-message: 4.0.2

  micromark-util-html-tag-name@2.0.1: {}

  micromark-util-normalize-identifier@2.0.1:
    dependencies:
      micromark-util-symbol: 2.0.1

  micromark-util-resolve-all@2.0.1:
    dependencies:
      micromark-util-types: 2.0.2

  micromark-util-sanitize-uri@2.0.1:
    dependencies:
      micromark-util-character: 2.1.1
      micromark-util-encode: 2.0.1
      micromark-util-symbol: 2.0.1

  micromark-util-subtokenize@2.1.0:
    dependencies:
      devlop: 1.1.0
      micromark-util-chunked: 2.0.1
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2

  micromark-util-symbol@2.0.1: {}

  micromark-util-types@2.0.2: {}

  micromark@4.0.2:
    dependencies:
      "@types/debug": 4.1.12
      debug: 4.4.1
      decode-named-character-reference: 1.2.0
      devlop: 1.1.0
      micromark-core-commonmark: 2.0.3
      micromark-factory-space: 2.0.1
      micromark-util-character: 2.1.1
      micromark-util-chunked: 2.0.1
      micromark-util-combine-extensions: 2.0.1
      micromark-util-decode-numeric-character-reference: 2.0.2
      micromark-util-encode: 2.0.1
      micromark-util-normalize-identifier: 2.0.1
      micromark-util-resolve-all: 2.0.1
      micromark-util-sanitize-uri: 2.0.1
      micromark-util-subtokenize: 2.1.0
      micromark-util-symbol: 2.0.1
      micromark-util-types: 2.0.2
    transitivePeerDependencies:
      - supports-color

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mimic-response@3.1.0: {}

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mkdirp-classic@0.5.3: {}

  mkdirp@3.0.1: {}

  motion-dom@12.20.1:
    dependencies:
      motion-utils: 12.19.0

  motion-utils@12.19.0: {}

  mrmime@2.0.1: {}

  ms@2.1.3: {}

  muggle-string@0.4.1: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.11: {}

  napi-build-utils@2.0.0: {}

  neotraverse@0.6.18: {}

  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  nlcst-to-string@4.0.0:
    dependencies:
      "@types/nlcst": 2.0.3

  node-abi@3.75.0:
    dependencies:
      semver: 7.7.2

  node-addon-api@7.1.1: {}

  node-fetch-native@1.6.6: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-mock-http@1.0.0: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  nwsapi@2.2.20: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  ofetch@1.4.1:
    dependencies:
      destr: 2.0.5
      node-fetch-native: 1.6.6
      ufo: 1.6.1

  ohash@2.0.11: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  oniguruma-parser@0.12.1: {}

  oniguruma-to-es@4.3.3:
    dependencies:
      oniguruma-parser: 0.12.1
      regex: 6.0.1
      regex-recursion: 6.0.2

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@6.2.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-queue@8.1.0:
    dependencies:
      eventemitter3: 5.0.1
      p-timeout: 6.1.4

  p-timeout@6.1.4: {}

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  package-manager-detector@1.3.0: {}

  pako@0.2.9: {}

  pako@1.0.11: {}

  parse-css-color@0.2.1:
    dependencies:
      color-name: 1.1.4
      hex-rgb: 4.3.0

  parse-entities@4.0.2:
    dependencies:
      "@types/unist": 2.0.11
      character-entities-legacy: 3.0.0
      character-reference-invalid: 2.0.1
      decode-named-character-reference: 1.2.0
      is-alphanumerical: 2.0.1
      is-decimal: 2.0.1
      is-hexadecimal: 2.0.1

  parse-latin@7.0.0:
    dependencies:
      "@types/nlcst": 2.0.3
      "@types/unist": 3.0.3
      nlcst-to-string: 4.0.0
      unist-util-modify-children: 4.0.0
      unist-util-visit-children: 3.0.0
      vfile: 6.0.3

  parse5@7.3.0:
    dependencies:
      entities: 6.0.1

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  pathe@2.0.3: {}

  pathval@2.0.0: {}

  pdf-lib@1.17.1:
    dependencies:
      "@pdf-lib/standard-fonts": 1.0.0
      "@pdf-lib/upng": 1.0.1
      pako: 1.0.11
      tslib: 1.14.1

  pdfjs-dist@5.3.31:
    optionalDependencies:
      "@napi-rs/canvas": 0.1.71

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pify@2.3.0: {}

  pirates@4.0.7: {}

  pngjs@5.0.0: {}

  postcss-css-variables@0.18.0(postcss@8.4.31):
    dependencies:
      balanced-match: 1.0.2
      escape-string-regexp: 1.0.5
      extend: 3.0.2
      postcss: 8.4.31

  postcss-import@15.1.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10

  postcss-js@4.0.1(postcss@8.5.6):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.6

  postcss-load-config@4.0.2(postcss@8.5.6):
    dependencies:
      lilconfig: 3.1.3
      yaml: 2.8.0
    optionalDependencies:
      postcss: 8.5.6

  postcss-nested@6.2.0(postcss@8.5.6):
    dependencies:
      postcss: 8.5.6
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prebuild-install@7.1.3:
    dependencies:
      detect-libc: 2.0.4
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 2.0.0
      node-abi: 3.75.0
      pump: 3.0.3
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.1.3
      tunnel-agent: 0.6.0

  prettier@2.8.7:
    optional: true

  prismjs@1.30.0: {}

  process-nextick-args@2.0.1: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  property-information@6.5.0: {}

  property-information@7.1.0: {}

  pump@3.0.3:
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0

  punycode@2.3.1: {}

  qrcode@1.5.4:
    dependencies:
      dijkstrajs: 1.0.3
      pngjs: 5.0.0
      yargs: 15.4.1

  queue-microtask@1.2.3: {}

  radix3@1.1.2: {}

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-icons@5.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-refresh@0.17.0: {}

  react-remove-scroll-bar@2.3.8(@types/react@19.1.8)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-style-singleton: 2.2.3(@types/react@19.1.8)(react@19.1.0)
      tslib: 2.8.1
    optionalDependencies:
      "@types/react": 19.1.8

  react-remove-scroll@2.7.1(@types/react@19.1.8)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.8)(react@19.1.0)
      react-style-singleton: 2.2.3(@types/react@19.1.8)(react@19.1.0)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.8)(react@19.1.0)
      use-sidecar: 1.1.3(@types/react@19.1.8)(react@19.1.0)
    optionalDependencies:
      "@types/react": 19.1.8

  react-style-singleton@2.2.3(@types/react@19.1.8)(react@19.1.0):
    dependencies:
      get-nonce: 1.0.1
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      "@types/react": 19.1.8

  react-window@1.8.11(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      "@babel/runtime": 7.27.6
      memoize-one: 5.2.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  react@19.1.0: {}

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  recma-build-jsx@1.0.0:
    dependencies:
      "@types/estree": 1.0.8
      estree-util-build-jsx: 3.0.1
      vfile: 6.0.3

  recma-jsx@1.0.0(acorn@8.15.0):
    dependencies:
      acorn-jsx: 5.3.2(acorn@8.15.0)
      estree-util-to-js: 2.0.0
      recma-parse: 1.0.0
      recma-stringify: 1.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - acorn

  recma-parse@1.0.0:
    dependencies:
      "@types/estree": 1.0.8
      esast-util-from-js: 2.0.1
      unified: 11.0.5
      vfile: 6.0.3

  recma-stringify@1.0.0:
    dependencies:
      "@types/estree": 1.0.8
      estree-util-to-js: 2.0.0
      unified: 11.0.5
      vfile: 6.0.3

  regex-recursion@6.0.2:
    dependencies:
      regex-utilities: 2.3.0

  regex-utilities@2.3.0: {}

  regex@6.0.1:
    dependencies:
      regex-utilities: 2.3.0

  rehype-parse@9.0.1:
    dependencies:
      "@types/hast": 3.0.4
      hast-util-from-html: 2.0.3
      unified: 11.0.5

  rehype-raw@7.0.0:
    dependencies:
      "@types/hast": 3.0.4
      hast-util-raw: 9.1.0
      vfile: 6.0.3

  rehype-recma@1.0.0:
    dependencies:
      "@types/estree": 1.0.8
      "@types/hast": 3.0.4
      hast-util-to-estree: 3.1.3
    transitivePeerDependencies:
      - supports-color

  rehype-stringify@10.0.1:
    dependencies:
      "@types/hast": 3.0.4
      hast-util-to-html: 9.0.5
      unified: 11.0.5

  rehype@13.0.2:
    dependencies:
      "@types/hast": 3.0.4
      rehype-parse: 9.0.1
      rehype-stringify: 10.0.1
      unified: 11.0.5

  remark-gfm@4.0.1:
    dependencies:
      "@types/mdast": 4.0.4
      mdast-util-gfm: 3.1.0
      micromark-extension-gfm: 3.0.0
      remark-parse: 11.0.0
      remark-stringify: 11.0.0
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-mdx@3.1.0:
    dependencies:
      mdast-util-mdx: 3.0.0
      micromark-extension-mdxjs: 3.0.0
    transitivePeerDependencies:
      - supports-color

  remark-parse@11.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      mdast-util-from-markdown: 2.0.2
      micromark-util-types: 2.0.2
      unified: 11.0.5
    transitivePeerDependencies:
      - supports-color

  remark-rehype@11.1.2:
    dependencies:
      "@types/hast": 3.0.4
      "@types/mdast": 4.0.4
      mdast-util-to-hast: 13.2.0
      unified: 11.0.5
      vfile: 6.0.3

  remark-smartypants@3.0.2:
    dependencies:
      retext: 9.0.0
      retext-smartypants: 6.2.0
      unified: 11.0.5
      unist-util-visit: 5.0.0

  remark-stringify@11.0.0:
    dependencies:
      "@types/mdast": 4.0.4
      mdast-util-to-markdown: 2.1.2
      unified: 11.0.5

  request-light@0.5.8: {}

  request-light@0.7.0: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-main-filename@2.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restructure@3.0.2: {}

  retext-latin@4.0.0:
    dependencies:
      "@types/nlcst": 2.0.3
      parse-latin: 7.0.0
      unified: 11.0.5

  retext-smartypants@6.2.0:
    dependencies:
      "@types/nlcst": 2.0.3
      nlcst-to-string: 4.0.0
      unist-util-visit: 5.0.0

  retext-stringify@4.0.0:
    dependencies:
      "@types/nlcst": 2.0.3
      nlcst-to-string: 4.0.0
      unified: 11.0.5

  retext@9.0.0:
    dependencies:
      "@types/nlcst": 2.0.3
      retext-latin: 4.0.0
      retext-stringify: 4.0.0
      unified: 11.0.5

  reusify@1.1.0: {}

  rollup@4.43.0:
    dependencies:
      "@types/estree": 1.0.7
    optionalDependencies:
      "@rollup/rollup-android-arm-eabi": 4.43.0
      "@rollup/rollup-android-arm64": 4.43.0
      "@rollup/rollup-darwin-arm64": 4.43.0
      "@rollup/rollup-darwin-x64": 4.43.0
      "@rollup/rollup-freebsd-arm64": 4.43.0
      "@rollup/rollup-freebsd-x64": 4.43.0
      "@rollup/rollup-linux-arm-gnueabihf": 4.43.0
      "@rollup/rollup-linux-arm-musleabihf": 4.43.0
      "@rollup/rollup-linux-arm64-gnu": 4.43.0
      "@rollup/rollup-linux-arm64-musl": 4.43.0
      "@rollup/rollup-linux-loongarch64-gnu": 4.43.0
      "@rollup/rollup-linux-powerpc64le-gnu": 4.43.0
      "@rollup/rollup-linux-riscv64-gnu": 4.43.0
      "@rollup/rollup-linux-riscv64-musl": 4.43.0
      "@rollup/rollup-linux-s390x-gnu": 4.43.0
      "@rollup/rollup-linux-x64-gnu": 4.43.0
      "@rollup/rollup-linux-x64-musl": 4.43.0
      "@rollup/rollup-win32-arm64-msvc": 4.43.0
      "@rollup/rollup-win32-ia32-msvc": 4.43.0
      "@rollup/rollup-win32-x64-msvc": 4.43.0
      fsevents: 2.3.3

  rrweb-cssom@0.8.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-buffer@5.1.2: {}

  safer-buffer@2.1.2: {}

  satori@0.15.2:
    dependencies:
      "@shuding/opentype.js": 1.4.0-beta.0
      css-background-parser: 0.1.0
      css-box-shadow: 1.0.0-3
      css-gradient-parser: 0.0.16
      css-to-react-native: 3.2.0
      emoji-regex-xs: 2.0.1
      escape-html: 1.0.3
      linebreak: 1.1.0
      parse-css-color: 0.2.1
      postcss-value-parser: 4.2.0
      yoga-wasm-web: 0.3.3

  sax@1.4.1: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.26.0: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  set-blocking@2.0.0: {}

  setimmediate@1.0.5: {}

  sharp@0.33.5:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.4
      semver: 7.7.2
    optionalDependencies:
      "@img/sharp-darwin-arm64": 0.33.5
      "@img/sharp-darwin-x64": 0.33.5
      "@img/sharp-libvips-darwin-arm64": 1.0.4
      "@img/sharp-libvips-darwin-x64": 1.0.4
      "@img/sharp-libvips-linux-arm": 1.0.5
      "@img/sharp-libvips-linux-arm64": 1.0.4
      "@img/sharp-libvips-linux-s390x": 1.0.4
      "@img/sharp-libvips-linux-x64": 1.0.4
      "@img/sharp-libvips-linuxmusl-arm64": 1.0.4
      "@img/sharp-libvips-linuxmusl-x64": 1.0.4
      "@img/sharp-linux-arm": 0.33.5
      "@img/sharp-linux-arm64": 0.33.5
      "@img/sharp-linux-s390x": 0.33.5
      "@img/sharp-linux-x64": 0.33.5
      "@img/sharp-linuxmusl-arm64": 0.33.5
      "@img/sharp-linuxmusl-x64": 0.33.5
      "@img/sharp-wasm32": 0.33.5
      "@img/sharp-win32-ia32": 0.33.5
      "@img/sharp-win32-x64": 0.33.5
    optional: true

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shiki@3.7.0:
    dependencies:
      "@shikijs/core": 3.7.0
      "@shikijs/engine-javascript": 3.7.0
      "@shikijs/engine-oniguruma": 3.7.0
      "@shikijs/langs": 3.7.0
      "@shikijs/themes": 3.7.0
      "@shikijs/types": 3.7.0
      "@shikijs/vscode-textmate": 10.0.2
      "@types/hast": 3.0.4

  siginfo@2.0.0: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1: {}

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2
    optional: true

  sisteransi@1.0.5: {}

  sitemap@8.0.0:
    dependencies:
      "@types/node": 17.0.45
      "@types/sax": 1.2.7
      arg: 5.0.2
      sax: 1.4.1

  smol-toml@1.3.4: {}

  sonner@2.0.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  source-map-js@1.2.1: {}

  source-map@0.7.4: {}

  space-separated-tokens@2.0.2: {}

  stackback@0.0.2: {}

  std-env@3.9.0: {}

  stream-replace-string@2.0.0: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string.prototype.codepointat@0.2.1: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  stringify-entities@4.0.4:
    dependencies:
      character-entities-html4: 2.1.0
      character-entities-legacy: 3.0.0

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-json-comments@2.0.1: {}

  strip-literal@3.0.0:
    dependencies:
      js-tokens: 9.0.1

  style-to-js@1.1.17:
    dependencies:
      style-to-object: 1.0.9

  style-to-object@1.0.9:
    dependencies:
      inline-style-parser: 0.2.4

  sucrase@3.35.0:
    dependencies:
      "@jridgewell/gen-mapping": 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.7
      ts-interface-checker: 0.1.13

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  symbol-tree@3.2.4: {}

  tailwind-merge@3.3.1: {}

  tailwindcss@3.3.2:
    dependencies:
      "@alloc/quick-lru": 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.7
      lilconfig: 2.1.0
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.6
      postcss-import: 15.1.0(postcss@8.5.6)
      postcss-js: 4.0.1(postcss@8.5.6)
      postcss-load-config: 4.0.2(postcss@8.5.6)
      postcss-nested: 6.2.0(postcss@8.5.6)
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  tailwindcss@4.1.11: {}

  tapable@2.2.2: {}

  tar-fs@2.1.3:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.3
      tar-stream: 2.2.0

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.5
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@7.4.3:
    dependencies:
      "@isaacs/fs-minipass": 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  test-exclude@7.0.1:
    dependencies:
      "@istanbuljs/schema": 0.1.3
      glob: 10.4.5
      minimatch: 9.0.5

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  tiny-inflate@1.0.3: {}

  tinybench@2.9.0: {}

  tinyexec@0.3.2: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2

  tinypool@1.1.1: {}

  tinyrainbow@2.0.0: {}

  tinyspy@4.0.3: {}

  tldts-core@6.1.86: {}

  tldts@6.1.86:
    dependencies:
      tldts-core: 6.1.86

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tough-cookie@5.1.2:
    dependencies:
      tldts: 6.1.86

  tr46@0.0.3: {}

  tr46@5.1.1:
    dependencies:
      punycode: 2.3.1

  trim-lines@3.0.1: {}

  trough@2.2.0: {}

  ts-interface-checker@0.1.13: {}

  tsconfck@3.1.6(typescript@5.8.3):
    optionalDependencies:
      typescript: 5.8.3

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsx@4.20.3:
    dependencies:
      esbuild: 0.25.5
      get-tsconfig: 4.10.1
    optionalDependencies:
      fsevents: 2.3.3

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.1.2

  tw-animate-css@1.3.4: {}

  tw-to-css@0.0.12:
    dependencies:
      postcss: 8.4.31
      postcss-css-variables: 0.18.0(postcss@8.4.31)
      tailwindcss: 3.3.2
    transitivePeerDependencies:
      - ts-node

  type-fest@4.41.0: {}

  typesafe-path@0.2.2: {}

  typescript-auto-import-cache@0.3.6:
    dependencies:
      semver: 7.7.2

  typescript@5.8.3: {}

  ufo@1.6.1: {}

  ultrahtml@1.6.0: {}

  uncrypto@0.1.3: {}

  undici-types@6.21.0: {}

  undici-types@7.8.0: {}

  unicode-properties@1.4.1:
    dependencies:
      base64-js: 1.5.1
      unicode-trie: 2.0.0

  unicode-trie@2.0.0:
    dependencies:
      pako: 0.2.9
      tiny-inflate: 1.0.3

  unified@11.0.5:
    dependencies:
      "@types/unist": 3.0.3
      bail: 2.0.2
      devlop: 1.1.0
      extend: 3.0.2
      is-plain-obj: 4.1.0
      trough: 2.2.0
      vfile: 6.0.3

  unifont@0.5.0:
    dependencies:
      css-tree: 3.1.0
      ohash: 2.0.11

  unist-util-find-after@5.0.0:
    dependencies:
      "@types/unist": 3.0.3
      unist-util-is: 6.0.0

  unist-util-is@6.0.0:
    dependencies:
      "@types/unist": 3.0.3

  unist-util-modify-children@4.0.0:
    dependencies:
      "@types/unist": 3.0.3
      array-iterate: 2.0.1

  unist-util-position-from-estree@2.0.0:
    dependencies:
      "@types/unist": 3.0.3

  unist-util-position@5.0.0:
    dependencies:
      "@types/unist": 3.0.3

  unist-util-remove-position@5.0.0:
    dependencies:
      "@types/unist": 3.0.3
      unist-util-visit: 5.0.0

  unist-util-stringify-position@4.0.0:
    dependencies:
      "@types/unist": 3.0.3

  unist-util-visit-children@3.0.0:
    dependencies:
      "@types/unist": 3.0.3

  unist-util-visit-parents@6.0.1:
    dependencies:
      "@types/unist": 3.0.3
      unist-util-is: 6.0.0

  unist-util-visit@5.0.0:
    dependencies:
      "@types/unist": 3.0.3
      unist-util-is: 6.0.0
      unist-util-visit-parents: 6.0.1

  unstorage@1.16.0:
    dependencies:
      anymatch: 3.1.3
      chokidar: 4.0.3
      destr: 2.0.5
      h3: 1.15.3
      lru-cache: 10.4.3
      node-fetch-native: 1.6.6
      ofetch: 1.4.1
      ufo: 1.6.1

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  use-callback-ref@1.3.3(@types/react@19.1.8)(react@19.1.0):
    dependencies:
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      "@types/react": 19.1.8

  use-sidecar@1.1.3(@types/react@19.1.8)(react@19.1.0):
    dependencies:
      detect-node-es: 1.1.0
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      "@types/react": 19.1.8

  util-deprecate@1.0.2: {}

  uuid@11.1.0: {}

  vfile-location@5.0.3:
    dependencies:
      "@types/unist": 3.0.3
      vfile: 6.0.3

  vfile-message@4.0.2:
    dependencies:
      "@types/unist": 3.0.3
      unist-util-stringify-position: 4.0.0

  vfile@6.0.3:
    dependencies:
      "@types/unist": 3.0.3
      vfile-message: 4.0.2

  vite-node@3.2.4(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0):
    dependencies:
      cac: 6.7.14
      debug: 4.4.1
      es-module-lexer: 1.7.0
      pathe: 2.0.3
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - "@types/node"
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0):
    dependencies:
      esbuild: 0.25.5
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.6
      rollup: 4.43.0
      tinyglobby: 0.2.14
    optionalDependencies:
      "@types/node": 24.0.3
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.30.1
      tsx: 4.20.3
      yaml: 2.8.0

  vitefu@1.0.6(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)):
    optionalDependencies:
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)

  vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.0.3)(happy-dom@18.0.1)(jiti@2.4.2)(jsdom@26.1.0(canvas@3.1.2))(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0):
    dependencies:
      "@types/chai": 5.2.2
      "@vitest/expect": 3.2.4
      "@vitest/mocker": 3.2.4(vite@6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      "@vitest/pretty-format": 3.2.4
      "@vitest/runner": 3.2.4
      "@vitest/snapshot": 3.2.4
      "@vitest/spy": 3.2.4
      "@vitest/utils": 3.2.4
      chai: 5.2.0
      debug: 4.4.1
      expect-type: 1.2.1
      magic-string: 0.30.17
      pathe: 2.0.3
      picomatch: 4.0.2
      std-env: 3.9.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.14
      tinypool: 1.1.1
      tinyrainbow: 2.0.0
      vite: 6.3.5(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
      vite-node: 3.2.4(@types/node@24.0.3)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
      why-is-node-running: 2.3.0
    optionalDependencies:
      "@types/debug": 4.1.12
      "@types/node": 24.0.3
      happy-dom: 18.0.1
      jsdom: 26.1.0(canvas@3.1.2)
    transitivePeerDependencies:
      - jiti
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  volar-service-css@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      vscode-css-languageservice: 6.3.6
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-emmet@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      "@emmetio/css-parser": 0.4.0
      "@emmetio/html-matcher": 1.3.0
      "@vscode/emmet-helper": 2.11.0
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-html@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      vscode-html-languageservice: 5.5.0
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-prettier@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-typescript-twoslash-queries@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-typescript@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      path-browserify: 1.0.1
      semver: 7.7.2
      typescript-auto-import-cache: 0.3.6
      vscode-languageserver-textdocument: 1.0.12
      vscode-nls: 5.2.0
      vscode-uri: 3.1.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  volar-service-yaml@0.0.62(@volar/language-service@2.4.14):
    dependencies:
      vscode-uri: 3.1.0
      yaml-language-server: 1.15.0
    optionalDependencies:
      "@volar/language-service": 2.4.14

  vscode-css-languageservice@6.3.6:
    dependencies:
      "@vscode/l10n": 0.0.18
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-uri: 3.1.0

  vscode-html-languageservice@5.5.0:
    dependencies:
      "@vscode/l10n": 0.0.18
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-uri: 3.1.0

  vscode-json-languageservice@4.1.8:
    dependencies:
      jsonc-parser: 3.3.1
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-nls: 5.2.0
      vscode-uri: 3.1.0

  vscode-jsonrpc@6.0.0: {}

  vscode-jsonrpc@8.2.0: {}

  vscode-languageserver-protocol@3.16.0:
    dependencies:
      vscode-jsonrpc: 6.0.0
      vscode-languageserver-types: 3.16.0

  vscode-languageserver-protocol@3.17.5:
    dependencies:
      vscode-jsonrpc: 8.2.0
      vscode-languageserver-types: 3.17.5

  vscode-languageserver-textdocument@1.0.12: {}

  vscode-languageserver-types@3.16.0: {}

  vscode-languageserver-types@3.17.5: {}

  vscode-languageserver@7.0.0:
    dependencies:
      vscode-languageserver-protocol: 3.16.0

  vscode-languageserver@9.0.1:
    dependencies:
      vscode-languageserver-protocol: 3.17.5

  vscode-nls@5.2.0: {}

  vscode-uri@3.1.0: {}

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  wasm-feature-detect@1.8.0: {}

  web-namespaces@2.0.1: {}

  webidl-conversions@3.0.1: {}

  webidl-conversions@7.0.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@3.0.0: {}

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.2.0:
    dependencies:
      tr46: 5.1.1
      webidl-conversions: 7.0.0

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-module@2.0.1: {}

  which-pm-runs@1.1.0: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.3.0:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  widest-line@5.0.0:
    dependencies:
      string-width: 7.2.0

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@8.18.2: {}

  xml-name-validator@5.0.0: {}

  xmlchars@2.2.0: {}

  xxhash-wasm@1.1.0: {}

  y18n@4.0.3: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@5.0.0: {}

  yaml-language-server@1.15.0:
    dependencies:
      ajv: 8.17.1
      lodash: 4.17.21
      request-light: 0.5.8
      vscode-json-languageservice: 4.1.8
      vscode-languageserver: 7.0.0
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-nls: 5.2.0
      vscode-uri: 3.1.0
      yaml: 2.2.2
    optionalDependencies:
      prettier: 2.8.7

  yaml@2.2.2: {}

  yaml@2.8.0: {}

  yargs-parser@18.1.3:
    dependencies:
      camelcase: 5.3.1
      decamelize: 1.2.0

  yargs-parser@21.1.1: {}

  yargs@15.4.1:
    dependencies:
      cliui: 6.0.0
      decamelize: 1.2.0
      find-up: 4.1.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      require-main-filename: 2.0.0
      set-blocking: 2.0.0
      string-width: 4.2.3
      which-module: 2.0.1
      y18n: 4.0.3
      yargs-parser: 18.1.3

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@1.2.1: {}

  yocto-spinner@0.2.3:
    dependencies:
      yoctocolors: 2.1.1

  yoctocolors@2.1.1: {}

  yoga-wasm-web@0.3.3: {}

  zod-to-json-schema@3.24.5(zod@3.25.67):
    dependencies:
      zod: 3.25.67

  zod-to-ts@1.2.0(typescript@5.8.3)(zod@3.25.67):
    dependencies:
      typescript: 5.8.3
      zod: 3.25.67

  zod@3.25.67: {}

  zwitch@2.0.4: {}
