{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(pnpm dlx:*)", "Bash(ls:*)", "Bash(pnpm test:*)", "Bash(pnpm build)", "Bash(grep:*)", "Bash(pnpm list:*)", "<PERSON><PERSON>(true)", "Bash(for file in src/components/converters/{ImageResizer,ImageCompressor,SvgConverter,ImageConverterComlink}.tsx)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(find:*)", "Bash(rg:*)", "Bash(pnpm run check:*)", "Bash(pnpm add:*)"], "deny": []}}